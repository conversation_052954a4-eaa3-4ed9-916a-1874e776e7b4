import React, { useState, useEffect } from "react";
import { Modal, Typography, Space, Divider, Image, Row, Col } from "antd";
import { GetVersion } from "../../wailsjs/go/main/App";
import { LogError } from "../../wailsjs/runtime/runtime";
import QRCodeImage from "../assets/images/qrcode_258.jpg";

const { Title, Paragraph, Text } = Typography;

interface AboutDialogProps {
  visible: boolean;
  onClose: () => void;
}

const AboutDialog: React.FC<AboutDialogProps> = ({ visible, onClose }) => {
  const [version, setVersion] = useState<string>("加载中...");

  useEffect(() => {
    if (visible) {
      // 当对话框显示时获取版本信息
      GetVersion()
        .then((ver) => {
          setVersion(ver);
        })
        .catch((err) => {
          LogError(`获取版本信息失败: ${err}`);
          setVersion("未知版本");
        });
    }
  }, [visible]);

  return (
    <Modal
      title="关于 山竹阅卷"
      open={visible}
      onCancel={onClose}
      footer={null}
      width={600}
      centered
    >
      <Row gutter={24}>
        <Col span={14}>
          <Space direction="vertical" style={{ width: "100%" }}>
            <div style={{ textAlign: "center", marginBottom: 16 }}>
              <Title level={4}>山竹阅卷</Title>
              <Paragraph>智能评分系统</Paragraph>
            </div>

            <Divider style={{ margin: "12px 0" }} />

            <Paragraph>
              <Text strong>版本：</Text> {version}
            </Paragraph>

            <Paragraph>
              <Text strong>版权所有：</Text> © 2025 ShanZhuLab
            </Paragraph>

            <Paragraph>
              <Text type="secondary">
                本软件用于自动化评分流程，提高评分效率和准确性。
              </Text>
            </Paragraph>
          </Space>
        </Col>
        <Col span={10}>
          <div style={{ textAlign: "center" }}>
            <Image
              src={QRCodeImage}
              alt="公众号二维码"
              preview={false}
              style={{ width: 180, height: 180 }}
            />
            <Paragraph style={{ marginTop: 8 }}>
              <Text strong>扫码关注公众号</Text>
            </Paragraph>
            <Paragraph>
              <Text type="secondary">获取更多使用技巧和支持</Text>
            </Paragraph>
          </div>
        </Col>
      </Row>
    </Modal>
  );
};

export default AboutDialog;
