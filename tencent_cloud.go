package main

import (
	"context"
	"crypto/hmac"
	"crypto/sha256"
	"encoding/base64"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strconv"
	"strings"
	"sync"
	"time"
)

var PREFIX string

const (
	LOGIN_PATH           = "/api/v1/auth/login"
	REGISTER_PATH        = "/api/v1/auth/register"
	REFRESH_PATH         = "/api/v1/auth/refresh"
	RECOVER_PATH         = "/api/v1/auth/recover"
	VERIFY_TOKEN_PATH    = "/api/v1/auth/verify"
	UPDATE_PASSWORD_PATH = "/api/v1/auth/update-password"
	BALANCE_PATH         = "/api/v1/wallet/balance"
	TRANSACTIONS_PATH    = "/api/v1/wallet/transactions"
	RECHARGE_PATH        = "/api/v1/recharge/request"
	RECHARGE_LIST_PATH   = "/api/v1/recharge/list"
	CHAT_PATH            = "/api/v1/chat/completions"
	CHAT_V2_PATH         = "/api/v2/chat/completions"
	CHAT_ANALYSIS_PATH   = "/api/v2/chat/analysis"
	VERSION_PATH         = "/api/v1/version"
)

// LoginRequest represents the login request payload
type LoginRequest struct {
	Email    string `json:"email"`
	Password string `json:"password"`
}

// RegisterRequest represents the register request payload
type RegisterRequest struct {
	Email    string `json:"email"`
	Password string `json:"password"`
}

// Action represents a single action in the configuration
type Action struct {
	Index    int    `json:"index"`
	Type     string `json:"type"`
	Selector string `json:"selector"`
	Value    string `json:"value"`
}

// ConfigItem represents a single configuration item
type ConfigItem struct {
	ID      string   `json:"id"`
	Name    string   `json:"name"`
	Url     string   `json:"url"`
	Actions []Action `json:"actions"`
}

// Subject represents a single subject in the configuration
type Subject struct {
	SubjectID   string `json:"subject_id"`
	SubjectName string `json:"subject_name"`
}

// LoginResponse represents the response from the login API
type LoginResponse struct {
	AccessToken  string       `json:"access_token"`
	RefreshToken string       `json:"refresh_token"`
	ExpiresIn    int64        `json:"expires_in"`
	Config       []ConfigItem `json:"config"`
	Subjects     []Subject    `json:"subjects"`
}

// 全局变量，存储登录后返回的Config和Subjects
var (
	GlobalConfig   []ConfigItem
	GlobalSubjects []Subject
	ConfigMutex    sync.Mutex
)

// GetGlobalConfig 获取全局Config变量
func GetGlobalConfig() []ConfigItem {
	ConfigMutex.Lock()
	defer ConfigMutex.Unlock()

	// 返回一个副本，避免外部修改
	if len(GlobalConfig) == 0 {
		return nil
	}

	config := make([]ConfigItem, len(GlobalConfig))
	copy(config, GlobalConfig)
	return config
}

// GetGlobalSubjects 获取全局Subjects变量
func GetGlobalSubjects() []Subject {
	ConfigMutex.Lock()
	defer ConfigMutex.Unlock()

	// 返回一个副本，避免外部修改
	if len(GlobalSubjects) == 0 {
		return nil
	}

	subjects := make([]Subject, len(GlobalSubjects))
	copy(subjects, GlobalSubjects)
	return subjects
}

// TencentCloudClient 腾讯云客户端
type TencentCloudClient struct {
	BaseURL      string
	SecretID     string
	SecretKey    string
	HTTPClient   *http.Client
	AccessToken  *SecureToken
	RefreshToken *SecureToken
	ExpiresIn    int64
}

func NewTencentCloudClient(baseURL string) *TencentCloudClient {
	return &TencentCloudClient{
		BaseURL:      baseURL,
		SecretID:     "AKIDpPp592kGZXLvJTSEn9IG4pV0IPwKfppf",
		SecretKey:    "qYAb2MZdXIrGUXaWP6cv0sXAew1vc6MO",
		HTTPClient:   &http.Client{},
		AccessToken:  &SecureToken{},
		RefreshToken: &SecureToken{},
	}
}

// ChatRequest 聊天请求结构
type TencentCloudChatRequest struct {
	Model   string `json:"model"`
	Image   string `json:"image"`
	Subject string `json:"subject"`
	Content string `json:"content"`
}

// ChatRequest 聊天请求结构
type TencentCloudChatV2Request struct {
	Model       string  `json:"model"`
	Text        string  `json:"text"`
	PromptKey   string  `json:"prompt_key"`
	Content     string  `json:"content"`
	ContentType string  `json:"content_type"`
	Temperature float32 `json:"temperature"`
}

// VersionResponse 版本信息响应结构
type VersionResponse struct {
	Version     string `json:"version"`      // 最新版本号
	DownloadURL string `json:"download_url"` // 下载地址
	ForceUpdate bool   `json:"force_update"` // 是否强制更新
	UpdateLog   string `json:"update_log"`   // 更新日志
}

// RechargeRequest 充值请求结构
type RechargeRequest struct {
	Amount       float64 `json:"amount"`        // 充值金额（人民币）
	OrderID      string  `json:"order_id"`      // 支付宝订单号
	PaymentProof string  `json:"payment_proof"` // 支付凭证URL（可选）
}

// RechargeResponse 充值响应结构
type RechargeResponse struct {
	Success     bool   `json:"success"`     // 是否成功
	RechargeID  int    `json:"recharge_id"` // 充值记录ID
	Message     string `json:"message"`     // 消息
	RequestInfo struct {
		ID            int     `json:"id"`             // 记录ID
		UserID        string  `json:"user_id"`        // 用户ID
		RmbAmount     float64 `json:"rmb_amount"`     // 人民币金额
		OrderID       string  `json:"order_id"`       // 订单号
		Status        string  `json:"status"`         // 状态
		PaymentMethod string  `json:"payment_method"` // 支付方式
		PaymentProof  string  `json:"payment_proof"`  // 支付凭证
		CreatedAt     int64   `json:"created_at"`     // 创建时间
		UpdatedAt     int64   `json:"updated_at"`     // 更新时间
	} `json:"request_info"` // 请求信息
}

// RecoverPasswordRequest 密码重置请求结构
type RecoverPasswordRequest struct {
	Email string `json:"email"` // 用户邮箱
}

// RecoverPasswordResponse 密码重置响应结构
type RecoverPasswordResponse struct {
	Success bool   `json:"success"` // 是否成功
	Message string `json:"message"` // 消息
}

// UpdatePasswordRequest 更新密码请求结构
type UpdatePasswordRequest struct {
	Password string `json:"password"` // 新密码
}

// UpdatePasswordResponse 更新密码响应结构
type UpdatePasswordResponse struct {
	Success bool   `json:"success"` // 是否成功
	Message string `json:"message"` // 消息
}

// VerifyTokenRequest 验证令牌请求结构
type VerifyTokenRequest struct {
	Email string `json:"email"` // 用户邮箱
	Token string `json:"token"` // 验证令牌
	Type  string `json:"type"`  // 令牌类型，可以是 "recovery"(重置密码) 或 "signup"(注册验证)
}

// VerifyTokenResponse 验证令牌响应结构
type VerifyTokenResponse struct {
	Success      bool   `json:"success"`       // 是否成功
	Message      string `json:"message"`       // 消息
	AccessToken  string `json:"access_token"`  // 访问令牌
	RefreshToken string `json:"refresh_token"` // 刷新令牌
	ExpiresIn    int64  `json:"expires_in"`    // 过期时间（秒）
	ExpiresAt    int64  `json:"expires_at"`    // 过期时间戳
}

// 使用腾讯云函数进行聊天
func (c *TencentCloudClient) Chat(ctx context.Context, model, image, content, subject string) (*ChatResponse, error) {
	// 构建请求体
	reqBody := TencentCloudChatV2Request{
		Model:     model,
		Content:   image,
		PromptKey: subject,
		Text:      content,
	}
	payload, err := json.Marshal(reqBody)
	if err != nil {
		return nil, err
	}
	// 调用云函数
	req, err := http.NewRequestWithContext(
		ctx, http.MethodPost, getHost(c.BaseURL, CHAT_ANALYSIS_PATH), strings.NewReader(string(payload)))
	if err != nil {
		return nil, fmt.Errorf("创建请求失败: %v", err)
	}
	c.addHeaders(req, http.MethodPost, CHAT_ANALYSIS_PATH, string(payload))
	resp, err := c.HTTPClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("发送请求失败: %v", err)
	}
	defer resp.Body.Close()
	// 检查响应状态码
	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		// 返回特定错误码以便上层处理
		if resp.StatusCode == http.StatusUnauthorized {
			return nil, fmt.Errorf("401")
		}
		if resp.StatusCode == http.StatusPaymentRequired {
			return nil, fmt.Errorf("402")
		}
		return nil, fmt.Errorf("chat request failed with status: %d, body: %s", resp.StatusCode, string(body))
	}
	// 更新客户端Token（如果返回新Token）
	if newToken := resp.Header.Get("X-New-Access-Token"); newToken != "" {
		c.AccessToken.Set(newToken)
	}
	// 读取响应体
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应失败: %v", err)
	}
	// 解析返回结果
	var chatResponse ChatResponse
	if err := json.Unmarshal(body, &chatResponse); err != nil {
		return nil, fmt.Errorf("解析聊天响应失败: %v", err)
	}
	return &chatResponse, nil
}

func (c *TencentCloudClient) ChatV2(ctx context.Context, reqBody TencentCloudChatV2Request) (*ChatResponse, error) {
	payload, err := json.Marshal(reqBody)
	if err != nil {
		return nil, err
	}
	// 调用云函数
	req, err := http.NewRequestWithContext(
		ctx, http.MethodPost, getHost(c.BaseURL, CHAT_V2_PATH), strings.NewReader(string(payload)))
	if err != nil {
		return nil, fmt.Errorf("创建请求失败: %v", err)
	}
	c.addHeaders(req, http.MethodPost, CHAT_V2_PATH, string(payload))
	resp, err := c.HTTPClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("发送请求失败: %v", err)
	}
	defer resp.Body.Close()
	// 检查响应状态码
	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		// 返回特定错误码以便上层处理
		if resp.StatusCode == http.StatusUnauthorized {
			return nil, fmt.Errorf("401")
		}
		if resp.StatusCode == http.StatusPaymentRequired {
			return nil, fmt.Errorf("402")
		}
		return nil, fmt.Errorf("chat request failed with status: %d, body: %s", resp.StatusCode, string(body))
	}
	// 更新客户端Token（如果返回新Token）
	if newToken := resp.Header.Get("X-New-Access-Token"); newToken != "" {
		c.AccessToken.Set(newToken)
	}
	// 读取响应体
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应失败: %v", err)
	}
	// 解析返回结果
	var chatResponse ChatResponse
	if err := json.Unmarshal(body, &chatResponse); err != nil {
		return nil, fmt.Errorf("解析聊天响应失败: %v", err)
	}
	return &chatResponse, nil
}

func (c *TencentCloudClient) Report(ctx context.Context, records []GradingRecordListItem, content, subject string) (*ChatResponse, error) {
	// TODO: 根据阅卷记录生成报告
	return nil, nil
}

// CheckVersion 检查软件版本
// 返回: 版本信息响应, 错误
func (c *TencentCloudClient) CheckVersion(ctx context.Context) (*VersionResponse, error) {
	// 创建GET请求
	req, err := http.NewRequestWithContext(ctx, http.MethodGet, getHost(c.BaseURL, VERSION_PATH), nil)
	if err != nil {
		return nil, fmt.Errorf("创建版本检查请求失败: %v", err)
	}

	// 添加请求头
	c.addHeaders(req, http.MethodGet, VERSION_PATH, "")

	// 发送请求
	resp, err := c.HTTPClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("发送版本检查请求失败: %v", err)
	}
	defer resp.Body.Close()

	// 检查响应状态码
	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("版本检查请求失败，状态码: %d", resp.StatusCode)
	}

	// 读取响应体
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取版本信息响应失败: %v", err)
	}

	// 解析返回结果
	var versionResponse VersionResponse
	if err := json.Unmarshal(body, &versionResponse); err != nil {
		return nil, fmt.Errorf("解析版本信息响应失败: %v", err)
	}

	return &versionResponse, nil
}

// 使用腾讯云函数进行登录
func (c *TencentCloudClient) Login(ctx context.Context, email, password string) (*LoginResponse, error) {
	// 构建请求体
	reqBody := LoginRequest{
		Email:    email,
		Password: password,
	}
	payload, err := json.Marshal(reqBody)
	if err != nil {
		return nil, err
	}
	// 调用云函数
	req, err := http.NewRequestWithContext(
		ctx, http.MethodPost, getHost(c.BaseURL, LOGIN_PATH), strings.NewReader(string(payload)))
	if err != nil {
		return nil, fmt.Errorf("创建请求失败: %v", err)
	}
	c.addHeaders(req, http.MethodPost, LOGIN_PATH, string(payload))
	resp, err := c.HTTPClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("发送请求失败: %v", err)
	}
	defer resp.Body.Close()
	// 检查响应状态码
	if resp.StatusCode != http.StatusOK {
		// 读取错误信息
		body, _ := io.ReadAll(resp.Body)
		return nil, fmt.Errorf("login failed with status: %d, body: %s", resp.StatusCode, string(body))
	}
	// 读取响应体
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应失败: %v", err)
	}
	// 解析返回结果
	var loginResponse LoginResponse
	if err := json.Unmarshal(body, &loginResponse); err != nil {
		return nil, fmt.Errorf("解析登录响应失败: %v", err)
	}
	// 保存令牌
	c.AccessToken.Set(loginResponse.AccessToken)
	c.RefreshToken.Set(loginResponse.RefreshToken)
	c.ExpiresIn = loginResponse.ExpiresIn
	// 将Config和Subjects存储到全局变量中
	ConfigMutex.Lock()
	if len(loginResponse.Config) > 0 {
		GlobalConfig = loginResponse.Config
	}
	if len(loginResponse.Subjects) > 0 {
		GlobalSubjects = loginResponse.Subjects
	}
	ConfigMutex.Unlock()
	return &loginResponse, nil
}

// 使用腾讯云函数进行注册
func (c *TencentCloudClient) Register(ctx context.Context, email, password string) error {
	// 构建请求体
	reqBody := RegisterRequest{
		Email:    email,
		Password: password,
	}
	payload, err := json.Marshal(reqBody)
	if err != nil {
		return err
	}
	// 调用云函数
	req, err := http.NewRequestWithContext(
		ctx, http.MethodPost, getHost(c.BaseURL, REGISTER_PATH), strings.NewReader(string(payload)))
	if err != nil {
		return fmt.Errorf("创建请求失败: %v", err)
	}
	c.addHeaders(req, http.MethodPost, REGISTER_PATH, string(payload))
	resp, err := c.HTTPClient.Do(req)
	if err != nil {
		return fmt.Errorf("发送请求失败: %v", err)
	}
	defer resp.Body.Close()
	// 检查响应状态码
	if resp.StatusCode != http.StatusOK && resp.StatusCode != http.StatusCreated {
		// 读取错误信息
		body, _ := io.ReadAll(resp.Body)
		return fmt.Errorf("register failed with status: %d, body: %s", resp.StatusCode, string(body))
	}

	return nil
}

// RecoverPassword 发送密码重置邮件
func (c *TencentCloudClient) RecoverPassword(ctx context.Context, email string) (*RecoverPasswordResponse, error) {
	// 构建请求体
	reqBody := RecoverPasswordRequest{
		Email: email,
	}
	payload, err := json.Marshal(reqBody)
	if err != nil {
		return nil, fmt.Errorf("序列化请求体失败: %v", err)
	}

	// 创建POST请求
	req, err := http.NewRequestWithContext(
		ctx, http.MethodPost, getHost(c.BaseURL, RECOVER_PATH), strings.NewReader(string(payload)))
	if err != nil {
		return nil, fmt.Errorf("创建密码重置请求失败: %v", err)
	}

	// 添加请求头
	c.addHeaders(req, http.MethodPost, RECOVER_PATH, string(payload))

	// 发送请求
	resp, err := c.HTTPClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("发送密码重置请求失败: %v", err)
	}
	defer resp.Body.Close()

	// 检查响应状态码
	if resp.StatusCode != http.StatusOK {
		// 读取错误信息
		body, _ := io.ReadAll(resp.Body)
		return nil, fmt.Errorf("密码重置请求失败，状态码: %d, body: %s", resp.StatusCode, string(body))
	}

	// 读取响应体
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取密码重置响应失败: %v", err)
	}

	// 解析返回结果
	var recoverResponse RecoverPasswordResponse
	if err := json.Unmarshal(body, &recoverResponse); err != nil {
		return nil, fmt.Errorf("解析密码重置响应失败: %v", err)
	}

	return &recoverResponse, nil
}

// VerifyToken 验证令牌
// tokenType: 令牌类型，可以是 "recovery"(重置密码) 或 "signup"(注册验证)
func (c *TencentCloudClient) VerifyToken(ctx context.Context, email, token, tokenType string) (*VerifyTokenResponse, error) {
	// 构建请求体
	reqBody := VerifyTokenRequest{
		Email: email,
		Token: token,
		Type:  tokenType,
	}
	payload, err := json.Marshal(reqBody)
	if err != nil {
		return nil, fmt.Errorf("序列化请求体失败: %v", err)
	}
	// 创建POST请求
	req, err := http.NewRequestWithContext(
		ctx, http.MethodPost, getHost(c.BaseURL, VERIFY_TOKEN_PATH), strings.NewReader(string(payload)))
	if err != nil {
		return nil, fmt.Errorf("创建验证令牌请求失败: %v", err)
	}
	// 添加请求头
	c.addHeaders(req, http.MethodPost, VERIFY_TOKEN_PATH, string(payload))
	// 发送请求
	resp, err := c.HTTPClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("发送验证令牌请求失败: %v", err)
	}
	defer resp.Body.Close()
	// 检查响应状态码
	if resp.StatusCode != http.StatusOK {
		// 读取错误信息
		body, _ := io.ReadAll(resp.Body)
		return nil, fmt.Errorf("验证令牌请求失败，状态码: %d, body: %s", resp.StatusCode, string(body))
	}
	// 读取响应体
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取验证令牌响应失败: %v", err)
	}
	// 解析返回结果
	var verifyResponse VerifyTokenResponse
	if err := json.Unmarshal(body, &verifyResponse); err != nil {
		return nil, fmt.Errorf("解析验证令牌响应失败: %v", err)
	}
	// 保存令牌
	if verifyResponse.AccessToken != "" {
		c.AccessToken.Set(verifyResponse.AccessToken)
	}
	if verifyResponse.RefreshToken != "" {
		c.RefreshToken.Set(verifyResponse.RefreshToken)
	}
	if verifyResponse.ExpiresIn > 0 {
		c.ExpiresIn = verifyResponse.ExpiresIn
	}
	return &verifyResponse, nil
}

// UpdatePassword 更新用户密码
func (c *TencentCloudClient) UpdatePassword(ctx context.Context, password string) (*UpdatePasswordResponse, error) {
	// 构建请求体
	reqBody := UpdatePasswordRequest{
		Password: password,
	}
	payload, err := json.Marshal(reqBody)
	if err != nil {
		return nil, fmt.Errorf("序列化请求体失败: %v", err)
	}
	// 创建POST请求
	req, err := http.NewRequestWithContext(
		ctx, http.MethodPost, getHost(c.BaseURL, UPDATE_PASSWORD_PATH), strings.NewReader(string(payload)))
	if err != nil {
		return nil, fmt.Errorf("创建更新密码请求失败: %v", err)
	}
	// 添加请求头（包括访问令牌）
	c.addHeaders(req, http.MethodPost, UPDATE_PASSWORD_PATH, string(payload))
	// 发送请求
	resp, err := c.HTTPClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("发送更新密码请求失败: %v", err)
	}
	defer resp.Body.Close()
	// 检查响应状态码
	if resp.StatusCode != http.StatusOK {
		// 读取错误信息
		body, _ := io.ReadAll(resp.Body)
		// 返回特定错误码以便上层处理
		if resp.StatusCode == http.StatusUnauthorized {
			return nil, fmt.Errorf("401")
		}
		return nil, fmt.Errorf("更新密码请求失败，状态码: %d, body: %s", resp.StatusCode, string(body))
	}
	// 读取响应体
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取更新密码响应失败: %v", err)
	}
	// 解析返回结果
	var updateResponse UpdatePasswordResponse
	if err := json.Unmarshal(body, &updateResponse); err != nil {
		return nil, fmt.Errorf("解析更新密码响应失败: %v", err)
	}
	return &updateResponse, nil
}

func (c *TencentCloudClient) GetBalance(ctx context.Context) (int, error) {
	req, err := http.NewRequestWithContext(ctx, http.MethodGet, getHost(c.BaseURL, BALANCE_PATH), nil)
	if err != nil {
		return 0, fmt.Errorf("创建请求失败: %v", err)
	}
	c.addHeaders(req, http.MethodGet, BALANCE_PATH, "")
	resp, err := c.HTTPClient.Do(req)
	if err != nil {
		return 0, fmt.Errorf("发送请求失败: %v", err)
	}
	defer resp.Body.Close()
	// 检查响应状态码
	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		// 返回特定错误码以便上层处理
		if resp.StatusCode == http.StatusUnauthorized {
			return 0, fmt.Errorf("401")
		}
		return 0, fmt.Errorf("获取余额失败，状态码: %d, body: %s", resp.StatusCode, string(body))
	}

	// 更新客户端Token（如果返回新Token）
	if newToken := resp.Header.Get("X-New-Access-Token"); newToken != "" {
		c.AccessToken.Set(newToken)
	}
	// 读取响应体
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return 0, fmt.Errorf("读取响应失败: %v", err)
	}
	// 解析返回结果
	var balanceResp struct {
		Balance int `json:"balance"`
	}
	if err := json.Unmarshal(body, &balanceResp); err != nil {
		return 0, fmt.Errorf("解析余额响应失败: %v", err)
	}
	return balanceResp.Balance, nil
}

// SubmitRecharge 提交充值请求
// amount: 充值金额（人民币）
// orderID: 支付宝订单号
// paymentProof: 支付凭证URL（可选）
// 返回: 充值响应, 错误
func (c *TencentCloudClient) SubmitRecharge(ctx context.Context, amount float64, orderID, paymentProof string) (*RechargeResponse, error) {
	// 构建请求体
	reqBody := RechargeRequest{
		Amount:       amount,
		OrderID:      orderID,
		PaymentProof: paymentProof,
	}
	payload, err := json.Marshal(reqBody)
	if err != nil {
		return nil, fmt.Errorf("序列化请求体失败: %v", err)
	}

	// 创建POST请求
	req, err := http.NewRequestWithContext(
		ctx, http.MethodPost, getHost(c.BaseURL, RECHARGE_PATH), strings.NewReader(string(payload)))
	if err != nil {
		return nil, fmt.Errorf("创建充值请求失败: %v", err)
	}

	// 添加请求头
	c.addHeaders(req, http.MethodPost, RECHARGE_PATH, string(payload))

	// 发送请求
	resp, err := c.HTTPClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("发送充值请求失败: %v", err)
	}
	defer resp.Body.Close()

	// 检查响应状态码
	if resp.StatusCode != http.StatusOK {
		// 读取错误信息
		body, _ := io.ReadAll(resp.Body)
		// 返回特定错误码以便上层处理
		if resp.StatusCode == http.StatusUnauthorized {
			return nil, fmt.Errorf("401")
		}
		return nil, fmt.Errorf("充值请求失败，状态码: %d, body: %s", resp.StatusCode, string(body))
	}

	// 更新客户端Token（如果返回新Token）
	if newToken := resp.Header.Get("X-New-Access-Token"); newToken != "" {
		c.AccessToken.Set(newToken)
	}

	// 读取响应体
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取充值响应失败: %v", err)
	}

	// 解析返回结果
	var rechargeResponse RechargeResponse
	if err := json.Unmarshal(body, &rechargeResponse); err != nil {
		return nil, fmt.Errorf("解析充值响应失败: %v", err)
	}

	return &rechargeResponse, nil
}

// GetRechargeList 获取充值记录列表
// limit: 每页数量，默认20
// offset: 偏移量，默认0
// 返回: 充值记录列表, 错误
func (c *TencentCloudClient) GetRechargeList(ctx context.Context, limit, offset int) (map[string]interface{}, error) {
	// 构建查询参数
	query := fmt.Sprintf("?limit=%d&offset=%d", limit, offset)

	// 创建GET请求
	req, err := http.NewRequestWithContext(
		ctx, http.MethodGet, getHost(c.BaseURL, RECHARGE_LIST_PATH)+query, nil)
	if err != nil {
		return nil, fmt.Errorf("创建充值记录请求失败: %v", err)
	}

	// 添加请求头
	c.addHeaders(req, http.MethodGet, RECHARGE_LIST_PATH+query, "")

	// 发送请求
	resp, err := c.HTTPClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("发送充值记录请求失败: %v", err)
	}
	defer resp.Body.Close()

	// 检查响应状态码
	if resp.StatusCode != http.StatusOK {
		// 读取错误信息
		body, _ := io.ReadAll(resp.Body)
		// 返回特定错误码以便上层处理
		if resp.StatusCode == http.StatusUnauthorized {
			return nil, fmt.Errorf("401")
		}
		return nil, fmt.Errorf("获取充值记录失败，状态码: %d, body: %s", resp.StatusCode, string(body))
	}

	// 更新客户端Token（如果返回新Token）
	if newToken := resp.Header.Get("X-New-Access-Token"); newToken != "" {
		c.AccessToken.Set(newToken)
	}

	// 读取响应体
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取充值记录响应失败: %v", err)
	}

	// 解析返回结果
	var result map[string]interface{}
	if err := json.Unmarshal(body, &result); err != nil {
		return nil, fmt.Errorf("解析充值记录响应失败: %v", err)
	}

	return result, nil
}

// IsTokenValid 检查令牌是否有效
func (c *TencentCloudClient) IsTokenValid() bool {
	_, valid := c.AccessToken.Get()
	return valid
}

// GetAccessToken 获取访问令牌
func (c *TencentCloudClient) GetAccessToken() (string, bool) {
	return c.AccessToken.Get()
}

func (c *TencentCloudClient) addHeaders(req *http.Request, method, path, payload string) {
	// 生成签名
	algorithm := "TC3-HMAC-SHA256"
	service := "scf"
	timestamp := time.Now().Unix()
	// 步骤1: 构建规范请求字符串
	canonicalRequest := buildCanonicalRequest(method, path, "", c.BaseURL, payload)
	// 步骤2: 构建待签名字符串
	stringToSign := buildStringToSign(algorithm, timestamp, service, canonicalRequest)
	// 步骤3: 计算签名
	signature := calculateSignature(c.SecretKey, stringToSign, timestamp, service)
	// 步骤4: 构建授权头
	authorization := buildAuthorizationHeader(algorithm, c.SecretID, timestamp, service, "content-type;host", signature)

	req.Header.Set("Authorization", authorization)
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Host", c.BaseURL)
	req.Header.Set("X-Scf-Cam-Timestamp", strconv.FormatInt(timestamp, 10))
	req.Header.Set("X-Scf-Cam-Uin", "100016998379")
	// 添加软件版本信息头
	req.Header.Set("X-App-Version", APP_VERSION)
	// 如果是登录请求或注册请求，则不需要添加访问令牌和刷新令牌
	if path == LOGIN_PATH || path == REGISTER_PATH {
		return
	}
	// 获取访问令牌，如果有效则添加访问令牌头部
	t, valid := c.AccessToken.Get()
	if valid {
		// 添加访问令牌
		req.Header.Set("X-Access-Token", fmt.Sprintf("Bearer %s", t))
	}
	// 检查是否需要添加刷新令牌
	if isTokenExpiringSoon(t) {
		if t, valid := c.RefreshToken.Get(); valid {
			// 添加刷新令牌
			req.Header.Set("X-Refresh-Token", t)
		}
	}
}

func buildCanonicalRequest(method, uri, queryString, host, payload string) string {
	canonicalHeaders := fmt.Sprintf("content-type:%s\nhost:%s\n", "application/json", host)
	signedHeaders := "content-type;host"
	hashedRequestPayload := sha256hex(payload)
	return fmt.Sprintf("%s\n%s\n%s\n%s\n%s\n%s",
		method,
		uri,
		queryString,
		canonicalHeaders,
		signedHeaders,
		hashedRequestPayload)
}

func buildStringToSign(algorithm string, timestamp int64, service, canonicalRequest string) string {
	date := time.Unix(timestamp, 0).UTC().Format("2006-01-02")
	credentialScope := fmt.Sprintf("%s/%s/tc3_request", date, service)
	hashedCanonicalRequest := sha256hex(canonicalRequest)
	return fmt.Sprintf("%s\n%d\n%s\n%s",
		algorithm,
		timestamp,
		credentialScope,
		hashedCanonicalRequest)
}

func calculateSignature(secretKey, stringToSign string, timestamp int64, service string) string {
	date := time.Unix(timestamp, 0).UTC().Format("2006-01-02")
	secretDate := hmacsha256(date, "TC3"+secretKey)
	secretService := hmacsha256(service, secretDate)
	secretSigning := hmacsha256("tc3_request", secretService)
	return hex.EncodeToString([]byte(hmacsha256(stringToSign, secretSigning)))
}

func buildAuthorizationHeader(algorithm, secretId string, timestamp int64, service, signedHeaders, signature string) string {
	date := time.Unix(timestamp, 0).UTC().Format("2006-01-02")
	credentialScope := fmt.Sprintf("%s/%s/tc3_request", date, service)
	return fmt.Sprintf("%s Credential=%s/%s, SignedHeaders=%s, Signature=%s",
		algorithm,
		secretId,
		credentialScope,
		signedHeaders,
		signature)
}

func sha256hex(s string) string {
	b := sha256.Sum256([]byte(s))
	return hex.EncodeToString(b[:])
}

func hmacsha256(s, key string) string {
	hashed := hmac.New(sha256.New, []byte(key))
	hashed.Write([]byte(s))
	return string(hashed.Sum(nil))
}

func getHost(baseUrl, path string) string {
	return fmt.Sprintf("%s%s%s", PREFIX, baseUrl, path)
}

// 检查 Token 是否即将过期（剩余3分钟）
func isTokenExpiringSoon(token string) bool {
	parts := strings.Split(token, ".")
	if len(parts) != 3 {
		return false
	}
	payload, _ := base64.RawURLEncoding.DecodeString(parts[1])
	var claims struct {
		Exp int64 `json:"exp"`
	}
	json.Unmarshal(payload, &claims)
	return time.Until(time.Unix(claims.Exp, 0)) < 3*time.Minute
}
