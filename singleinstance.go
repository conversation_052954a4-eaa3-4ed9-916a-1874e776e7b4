package main

import (
	"fmt"
	"os"
	"path/filepath"
	"time"

	"github.com/adrg/xdg"
	"github.com/allan-simon/go-singleinstance"
)

// 锁文件句柄
var instanceLock *os.File

// 写入本地日志文件
func writeLocalLogFile(message string) {
	// 确保日志目录存在
	if err := os.MkdirAll(filepath.Join(xdg.ConfigHome, "ai-grading"), 0o755); err != nil {
		fmt.Printf("无法创建日志目录: %v\n", err)
		return
	}

	// 创建日志文件名
	logFileName := filepath.Join(xdg.ConfigHome, "ai-grading", "aig-app.log")

	// 准备日志内容
	timestamp := time.Now().Format("2006-01-02 15:04:05")
	logContent := fmt.Sprintf("[%s] %s\n", timestamp, message)

	// 写入日志文件
	file, err := os.OpenFile(logFileName, os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0o644)
	if err != nil {
		fmt.Printf("无法创建日志文件: %v\n", err)
		return
	}
	defer file.Close()

	_, err = file.WriteString(logContent)
	if err != nil {
		fmt.Printf("写入日志文件失败: %v\n", err)
		return
	}

	fmt.Printf("已将日志写入文件: %s\n", logFileName)
}

// 检查是否是单实例运行
// 返回值: 是否是第一个实例, 错误信息
func checkSingleInstance() (bool, error) {
	// 获取锁文件路径
	lockFilePath := filepath.Join(xdg.ConfigHome, "ai-grading", "app.lock")
	fmt.Printf("锁文件路径: %s\n", lockFilePath)

	// 确保锁文件目录存在
	lockDir := filepath.Dir(lockFilePath)
	if err := os.MkdirAll(lockDir, 0o755); err != nil {
		errMsg := fmt.Sprintf("无法创建锁文件目录: %v", err)
		fmt.Println(errMsg)

		// 写入本地日志文件
		writeLocalLogFile(errMsg)

		return false, fmt.Errorf("%s", errMsg)
	}

	// 尝试创建锁文件
	var err error
	instanceLock, err = singleinstance.CreateLockFile(lockFilePath)
	if err != nil {
		errMsg := fmt.Sprintf("检测到另一个实例正在运行: %v", err)
		fmt.Println(errMsg)

		// 写入本地日志文件
		writeLocalLogFile(errMsg)

		return false, nil
	}

	// 成功获取锁，这是第一个实例
	fmt.Printf("成功获取文件锁，这是第一个实例\n")
	return true, nil
}

// 释放单实例锁
func releaseSingleInstanceLock() {
	if instanceLock != nil {
		fmt.Printf("正在释放单实例锁\n")
		instanceLock.Close()
		instanceLock = nil
	}
}
