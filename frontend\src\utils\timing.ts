import { LogInfo } from "../../wailsjs/runtime/runtime";

/**
 * 计时工具类，用于测量代码执行时间
 */
export class Timer {
  private startTime: number;
  private checkpoints: { name: string; time: number }[] = [];
  private name: string;

  /**
   * 创建一个新的计时器
   * @param name 计时器名称，用于日志输出
   */
  constructor(name: string) {
    this.name = name;
    this.startTime = performance.now();
  }

  /**
   * 记录一个检查点
   * @param name 检查点名称
   */
  checkpoint(name: string): void {
    this.checkpoints.push({
      name,
      time: performance.now(),
    });
  }

  /**
   * 结束计时并输出所有时间点
   */
  end(): void {
    const endTime = performance.now();
    const totalDuration = endTime - this.startTime;

    // 构建日志消息
    let message = `[${this.name}] 总耗时: ${totalDuration.toFixed(2)}ms\n`;
    
    // 添加检查点信息
    if (this.checkpoints.length > 0) {
      let lastTime = this.startTime;
      
      this.checkpoints.forEach((checkpoint, index) => {
        const duration = checkpoint.time - lastTime;
        const percentOfTotal = ((duration / totalDuration) * 100).toFixed(1);
        message += `  - ${checkpoint.name}: ${duration.toFixed(2)}ms (${percentOfTotal}%)\n`;
        lastTime = checkpoint.time;
      });
      
      // 添加最后一段时间
      if (this.checkpoints.length > 0) {
        const lastCheckpoint = this.checkpoints[this.checkpoints.length - 1];
        const duration = endTime - lastCheckpoint.time;
        const percentOfTotal = ((duration / totalDuration) * 100).toFixed(1);
        message += `  - 完成: ${duration.toFixed(2)}ms (${percentOfTotal}%)\n`;
      }
    }
    
    // 输出日志
    LogInfo(message);
  }
}
