import React, { useState, useEffect } from "react";
import { Modal, Table, message, Image, Tooltip, Ty<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>confirm, DatePicker, Space, Select } from "antd";
import type { SorterResult, TablePaginationConfig, FilterValue } from "antd/es/table/interface";
import type { SortOrder } from "antd/es/table/interface";
import { DeleteOutlined, FileExcelOutlined, FileTextOutlined, SearchOutlined } from "@ant-design/icons";
import dayjs from 'dayjs';

const { Paragraph } = Typography;
// 定义评分标准项类型
interface GradingCriteriaItem {
  id: string;
  content: string;
}

// 声明 Go 函数
declare module "../../wailsjs/go/main/App" {
  export function GetGradingRecordsList(): Promise<{
    records: GradingRecordListItem[];
    total: number;
    error: string | null;
  }>;
  export function GetGradingRecordsListByTimeRange(startTime: Date, endTime: Date): Promise<{
    records: GradingRecordListItem[];
    total: number;
    error: string | null;
  }>;
  export function GetGradingRecordsListByTimeRangeAndCriteria(startTime: Date, endTime: Date, criteriaID: string): Promise<{
    records: GradingRecordListItem[];
    total: number;
    error: string | null;
  }>;
  export function GetGradingCriteriaList(): Promise<GradingCriteriaItem[]>;
  export function DeleteGradingRecords(imageHashes: string[]): Promise<{
    count: number;
    error: string | null;
  }>;
  export function ExportGradingRecordsToWord(exportDir: string): Promise<void>;
  export function ExportGradingRecordsToWordByTimeRange(exportDir: string, startTime: Date, endTime: Date): Promise<void>;
  export function ExportGradingRecordsToWordByTimeRangeAndCriteria(exportDir: string, startTime: Date, endTime: Date, criteriaID: string): Promise<void>;
  export function ExportGradingRecordsToExcel(exportDir: string): Promise<void>;
  export function ExportGradingRecordsToExcelByTimeRange(exportDir: string, startTime: Date, endTime: Date, hasReport: boolean): Promise<void>;
  export function ExportGradingRecordsToExcelByTimeRangeAndCriteria(exportDir: string, startTime: Date, endTime: Date, criteriaID: string, hasReport: boolean): Promise<void>;
  export function SelectExportDirectory(): Promise<string>;
}

import { GetGradingRecordsListByTimeRange, GetGradingRecordsListByTimeRangeAndCriteria, GetGradingCriteriaList, DeleteGradingRecords, ExportGradingRecordsToExcel, ExportGradingRecordsToExcelByTimeRange, ExportGradingRecordsToExcelByTimeRangeAndCriteria, ExportGradingRecordsToCSV, ExportGradingRecordsToCSVByTimeRange, ExportGradingRecordsToCSVByTimeRangeAndCriteria, SelectExportDirectory } from "../../wailsjs/go/main/App";
import { LogError, LogInfo } from "../../wailsjs/runtime/runtime";
import { useLoading } from "../contexts/LoadingContext";

// 定义阅卷记录列表项类型
interface GradingRecordListItem {
  created_at: string;
  user_email: string;
  answer_image_hash: string;
  score: number;
  answer_image: string; // Base64编码的图片
  grading_criteria: string; // 评分标准
  answer_text: string; // 学生答案文本
  score_details: string; // 得分细节
}

interface GradingRecordsDialogProps {
  visible: boolean;
  onClose: () => void;
}

const { RangePicker } = DatePicker;

const GradingRecordsDialog: React.FC<GradingRecordsDialogProps> = ({ visible, onClose }) => {
  const [records, setRecords] = useState<GradingRecordListItem[]>([]);
  const [sortField, setSortField] = useState<string | null>(null);
  const [sortOrder, setSortOrder] = useState<string | null>(null);
  const [selectedRowKeys, setSelectedRowKeys] = useState<string[]>([]);
  const [deleteLoading, setDeleteLoading] = useState<boolean>(false);
  const [exportLoading, setExportLoading] = useState<boolean>(false);
  const [dateRange, setDateRange] = useState(() => {
    // 默认选择最近15分钟
    const endTime = dayjs();
    const startTime = endTime.subtract(15, 'minute');
    return [startTime, endTime];
  });
  const [searchLoading, setSearchLoading] = useState<boolean>(false);
  const [criteriaList, setCriteriaList] = useState<GradingCriteriaItem[]>([]);
  const [selectedCriteriaId, setSelectedCriteriaId] = useState<string | null>(null);
  const [loadingCriteria, setLoadingCriteria] = useState<boolean>(false);
  const { setLoading } = useLoading();

  // 处理表格排序
  const handleTableChange = (
    _: TablePaginationConfig,
    __: Record<string, FilterValue | null>,
    sorter: SorterResult<GradingRecordListItem> | SorterResult<GradingRecordListItem>[]
  ) => {
    const sorterResult = Array.isArray(sorter) ? sorter[0] : sorter;
    setSortField(sorterResult.field as string);
    setSortOrder(sorterResult.order as string);
  };

  // 处理行选择变化
  const rowSelection = {
    selectedRowKeys,
    onChange: (selectedKeys: React.Key[]) => {
      setSelectedRowKeys(selectedKeys as string[]);
    },
  };

  // 处理删除选中记录
  const handleDelete = async () => {
    if (selectedRowKeys.length === 0) {
      message.warning("请选择要删除的记录");
      return;
    }

    setDeleteLoading(true);
    try {
      const result = await DeleteGradingRecords(selectedRowKeys);
      if (result.error) {
        message.error(`删除记录失败: ${result.error}`);
      } else {
        message.success(`成功删除 ${result.count} 条记录`);
        // 重置选中状态
        setSelectedRowKeys([]);
        // 重新加载数据
        loadRecords();
      }
    } catch (err) {
      LogError(`删除记录失败: ${err}`);
      message.error(`删除记录失败: ${err}`);
    } finally {
      setDeleteLoading(false);
    }
  };



  // 表格列定义
  const columns = [
    {
      title: "创建时间",
      dataIndex: "created_at",
      key: "created_at",
      render: (text: string) => new Date(text).toLocaleString(),
      width: 140,
      sorter: true,
      sortDirections: ["ascend", "descend"] as SortOrder[],
      defaultSortOrder: "descend" as SortOrder,
    },
    {
      title: "用户",
      dataIndex: "user_email",
      key: "user_email",
      width: 130,
    },
    {
      title: "分数",
      dataIndex: "score",
      key: "score",
      width: 60,
      sorter: true,
      sortDirections: ["ascend", "descend"] as SortOrder[],
    },
    {
      title: "答案图片",
      dataIndex: "answer_image",
      key: "answer_image",
      width: 110,
      render: (text: string) => <Image src={text} alt="答案图片" width={80} height={60} style={{ objectFit: "contain" }} />,
    },
    {
      title: "评分标准",
      dataIndex: "grading_criteria",
      key: "grading_criteria",
      width: 150,
      ellipsis: true,
      render: (text: string) => (
        <Tooltip title={text}>
          <Paragraph ellipsis={{ rows: 1 }}>{text}</Paragraph>
        </Tooltip>
      ),
    },
    {
      title: "学生答案",
      dataIndex: "answer_text",
      key: "answer_text",
      width: 150,
      ellipsis: true,
      render: (text: string) => (
        <Tooltip title={text}>
          <Paragraph ellipsis={{ rows: 1 }}>{text}</Paragraph>
        </Tooltip>
      ),
    },
    {
      title: "评分细节",
      dataIndex: "score_details",
      key: "score_details",
      width: 150,
      ellipsis: true,
      render: (text: string) => (
        <Tooltip title={text}>
          <Paragraph ellipsis={{ rows: 1 }}>{text}</Paragraph>
        </Tooltip>
      ),
    },
  ];

  // 处理日期范围变化
  const handleDateRangeChange = (dates: any) => {
    setDateRange(dates);
  };

  // 根据日期范围查询记录
  const searchByDateRange = async () => {
    if (!dateRange || dateRange.length < 2) {
      message.warning("请选择有效的日期时间范围");
      return;
    }

    setSearchLoading(true);
    setLoading(true);
    try {
      // 转换为 JavaScript Date 对象
      const startTime = dateRange[0].toDate();
      const endTime = dateRange[1].toDate();
      LogInfo(`开始按时间范围查询阅卷记录，开始时间: ${startTime}, 结束时间: ${endTime}`);

      // 调用后端函数查询指定时间范围的记录
      const result = await GetGradingRecordsListByTimeRange(startTime, endTime);

      // 在控制台输出返回结果以便于调试
      LogInfo(`按时间范围查询阅卷记录成功，共 ${result.total} 条记录`);

      if (result.error) {
        message.error(`加载阅卷记录失败: ${result.error}`);
        return;
      }

      let sortedRecords = [...(result.records || [])];

      // 客户端排序
      if (sortField && sortOrder) {
        sortedRecords.sort((a, b) => {
          let fieldA: any;
          let fieldB: any;

          if (sortField === "created_at") {
            fieldA = new Date(a.created_at).getTime();
            fieldB = new Date(b.created_at).getTime();
          } else if (sortField === "score") {
            fieldA = a.score;
            fieldB = b.score;
          } else {
            return 0;
          }

          if (sortOrder === "ascend") {
            return fieldA > fieldB ? 1 : -1;
          } else {
            return fieldA < fieldB ? 1 : -1;
          }
        });
      }

      setRecords(sortedRecords);
    } catch (err) {
      LogError(`按时间范围查询阅卷记录失败: ${err}`);
      message.error(`按时间范围查询阅卷记录失败: ${err}`);
    } finally {
      setLoading(false);
      setSearchLoading(false);
    }
  };

  // 加载阅卷记录
  const loadRecords = async () => {
    // 默认使用时间范围查询
    await searchByDateRange();
  };

  // 加载评分标准列表
  const loadGradingCriteriaList = async () => {
    setLoadingCriteria(true);
    try {
      const result = await GetGradingCriteriaList();
      LogInfo(`加载评分标准列表成功，共 ${result.length} 条记录`);
      setCriteriaList(result);
    } catch (err) {
      LogError(`加载评分标准列表失败: ${err}`);
      message.error(`加载评分标准列表失败: ${err}`);
    } finally {
      setLoadingCriteria(false);
    }
  };

  // 根据评分标准ID和时间范围查询记录
  const searchByTimeRangeAndCriteria = async () => {
    if (!dateRange || dateRange.length < 2) {
      message.warning("请选择有效的日期时间范围");
      return;
    }

    if (!selectedCriteriaId) {
      // 如果没有选择评分标准，则使用普通的时间范围查询
      await searchByDateRange();
      return;
    }

    setSearchLoading(true);
    setLoading(true);
    try {
      // 转换为 JavaScript Date 对象
      const startTime = dateRange[0].toDate();
      const endTime = dateRange[1].toDate();
      LogInfo(`开始按时间范围和评分标准查询阅卷记录，开始时间: ${startTime}, 结束时间: ${endTime}, 评分标准ID: ${selectedCriteriaId}`);

      // 调用后端函数查询指定时间范围和评分标准的记录
      const result = await GetGradingRecordsListByTimeRangeAndCriteria(startTime, endTime, selectedCriteriaId);

      // 在控制台输出返回结果以便于调试
      LogInfo(`按时间范围和评分标准查询阅卷记录成功，共 ${result.total} 条记录`);

      if (result.error) {
        message.error(`加载阅卷记录失败: ${result.error}`);
        return;
      }

      let sortedRecords = [...(result.records || [])];

      // 客户端排序
      if (sortField && sortOrder) {
        sortedRecords.sort((a, b) => {
          let fieldA: any;
          let fieldB: any;

          if (sortField === "created_at") {
            fieldA = new Date(a.created_at).getTime();
            fieldB = new Date(b.created_at).getTime();
          } else if (sortField === "score") {
            fieldA = a.score;
            fieldB = b.score;
          } else {
            return 0;
          }

          if (sortOrder === "ascend") {
            return fieldA > fieldB ? 1 : -1;
          } else {
            return fieldA < fieldB ? 1 : -1;
          }
        });
      }

      setRecords(sortedRecords);
    } catch (err) {
      LogError(`按时间范围和评分标准查询阅卷记录失败: ${err}`);
      message.error(`按时间范围和评分标准查询阅卷记录失败: ${err}`);
    } finally {
      setLoading(false);
      setSearchLoading(false);
    }
  };

  // 处理评分标准变化
  const handleCriteriaChange = (value: string | null) => {
    setSelectedCriteriaId(value);
  };

  // 当对话框可见时加载评分标准列表和记录
  useEffect(() => {
    if (visible) {
      loadGradingCriteriaList();
      loadRecords();
    }
  }, [visible, sortField, sortOrder]);



  // 导出为Word文档 - 暂时禁用
  /* const handleExportToWord = async () => {
    if (records.length === 0) {
      message.warning("没有记录可导出");
      return;
    }

    LogInfo("开始导出为Word文档");
    setExportLoading(true);
    try {
      // 选择导出目录
      LogInfo("显示目录选择对话框");
      const exportDir = await SelectExportDirectory();
      LogInfo(`用户选择的导出目录: ${exportDir || '默认目录'}`);

      // 如果有日期范围，则使用带时间范围的导出函数
      if (dateRange && dateRange.length === 2) {
        const startTime = dateRange[0].toDate();
        const endTime = dateRange[1].toDate();

        // 如果选择了评分标准，则使用带评分标准的导出函数
        if (selectedCriteriaId) {
          LogInfo(`使用时间范围和评分标准导出: ${startTime.toISOString()} - ${endTime.toISOString()}, 评分标准ID: ${selectedCriteriaId}`);
          await ExportGradingRecordsToWordByTimeRangeAndCriteria(exportDir || "", startTime, endTime, selectedCriteriaId);
        } else {
          // 否则只使用时间范围
          LogInfo(`使用时间范围导出: ${startTime.toISOString()} - ${endTime.toISOString()}`);
          await ExportGradingRecordsToWordByTimeRange(exportDir || "", startTime, endTime);
        }
      } else {
        // 否则使用普通导出函数
        await ExportGradingRecordsToWord(exportDir || "");
      }
    } catch (err) {
      LogError(`导出为Word失败: ${err}`);
      message.error(`导出失败: ${err}`);
    } finally {
      setExportLoading(false);
    }
  }; */

  // 导出为Excel表格
  const handleExportToExcel = async () => {
    if (records.length === 0) {
      message.warning("没有记录可导出");
      return;
    }

    LogInfo("开始导出为Excel表格");
    setExportLoading(true);
    try {
      // 选择导出目录
      LogInfo("显示目录选择对话框");
      const exportDir = await SelectExportDirectory();
      LogInfo(`用户选择的导出目录: ${exportDir || '默认目录'}`);

      // 如果有日期范围，则使用带时间范围的导出函数
      if (dateRange && dateRange.length === 2) {
        const startTime = dateRange[0].toDate();
        const endTime = dateRange[1].toDate();

        // 如果选择了评分标准，则使用带评分标准的导出函数
        if (selectedCriteriaId) {
          LogInfo(`使用时间范围和评分标准导出: ${startTime.toISOString()} - ${endTime.toISOString()}, 评分标准ID: ${selectedCriteriaId}`);
          await ExportGradingRecordsToExcelByTimeRangeAndCriteria(exportDir || "", startTime, endTime, selectedCriteriaId, false);
        } else {
          // 否则只使用时间范围
          LogInfo(`使用时间范围导出: ${startTime.toISOString()} - ${endTime.toISOString()}`);
          await ExportGradingRecordsToExcelByTimeRange(exportDir || "", startTime, endTime, false);
        }
      } else {
        // 否则使用普通导出函数
        await ExportGradingRecordsToExcel(exportDir || "");
      }
    } catch (err) {
      LogError(`导出为Excel失败: ${err}`);
      message.error(`导出失败: ${err}`);
    } finally {
      setExportLoading(false);
    }
  };

  // 导出为CSV文件
  const handleExportToCSV = async () => {
    if (records.length === 0) {
      message.warning("没有记录可导出");
      return;
    }

    LogInfo("开始导出为CSV文件");
    setExportLoading(true);
    try {
      // 选择导出目录
      LogInfo("显示目录选择对话框");
      const exportDir = await SelectExportDirectory();
      LogInfo(`用户选择的导出目录: ${exportDir || '默认目录'}`);

      // 如果有日期范围，则使用带时间范围的导出函数
      if (dateRange && dateRange.length === 2) {
        const startTime = dateRange[0].toDate();
        const endTime = dateRange[1].toDate();

        // 如果选择了评分标准，则使用带评分标准的导出函数
        if (selectedCriteriaId) {
          LogInfo(`使用时间范围和评分标准导出CSV: ${startTime.toISOString()} - ${endTime.toISOString()}, 评分标准ID: ${selectedCriteriaId}`);
          await ExportGradingRecordsToCSVByTimeRangeAndCriteria(exportDir || "", startTime, endTime, selectedCriteriaId);
        } else {
          // 否则只使用时间范围
          LogInfo(`使用时间范围导出CSV: ${startTime.toISOString()} - ${endTime.toISOString()}`);
          await ExportGradingRecordsToCSVByTimeRange(exportDir || "", startTime, endTime);
        }
      } else {
        // 否则使用普通导出函数
        await ExportGradingRecordsToCSV(exportDir || "");
      }
      message.success("导出CSV成功");
    } catch (err) {
      LogError(`导出为CSV失败: ${err}`);
      message.error(`导出失败: ${err}`);
    } finally {
      setExportLoading(false);
    }
  };

  // 导出分析报告 - 暂时禁用
  /* const handleExportAnalysisReport = async () => {
    if (records.length === 0) {
      message.warning("没有记录可导出分析");
      return;
    }

    LogInfo("开始导出分析报告");
    setExportLoading(true);
    try {
      // 选择导出目录
      LogInfo("显示目录选择对话框");
      const exportDir = await SelectExportDirectory();
      LogInfo(`用户选择的导出目录: ${exportDir || '默认目录'}`);

      // 如果有日期范围，则使用带时间范围的导出函数
      if (dateRange && dateRange.length === 2) {
        const startTime = dateRange[0].toDate();
        const endTime = dateRange[1].toDate();

        // 如果选择了评分标准，则使用带评分标准的导出函数
        if (selectedCriteriaId) {
          LogInfo(`使用时间范围和评分标准导出分析报告: ${startTime.toISOString()} - ${endTime.toISOString()}, 评分标准ID: ${selectedCriteriaId}`);
          await ExportGradingRecordsToExcelByTimeRangeAndCriteria(exportDir || "", startTime, endTime, selectedCriteriaId, true);
          message.success("分析报告生成中，请稍候...");
        } else {
          // 否则只使用时间范围
          LogInfo(`使用时间范围导出分析报告: ${startTime.toISOString()} - ${endTime.toISOString()}`);
          await ExportGradingRecordsToExcelByTimeRange(exportDir || "", startTime, endTime, true);
          message.success("分析报告生成中，请稍候...");
        }
      } else {
        message.warning("请选择时间范围以生成分析报告");
      }
    } catch (err) {
      LogError(`导出分析报告失败: ${err}`);
      message.error(`导出分析报告失败: ${err}`);
    } finally {
      setExportLoading(false);
    }
  }; */

  return (
    <Modal
      title="阅卷记录列表"
      open={visible}
      onCancel={onClose}
      width={1200}
      footer={
        <div style={{ display: 'flex', justifyContent: 'space-between' }}>
          <div>
            {/* 暂时禁用导出Word功能 */}
            {/* <Button
              icon={<FileWordOutlined />}
              onClick={handleExportToWord}
              loading={exportLoading}
              style={{ marginRight: 8 }}
            >
              导出为Word
            </Button> */}
            <Button
              icon={<FileExcelOutlined />}
              onClick={handleExportToExcel}
              loading={exportLoading}
              style={{ marginRight: 8 }}
            >
              导出为Excel
            </Button>
            <Button
              icon={<FileTextOutlined />}
              onClick={handleExportToCSV}
              loading={exportLoading}
              style={{ marginRight: 8 }}
            >
              导出为CSV
            </Button>
            {/* 暂时禁用导出分析报告功能 */}
            {/* <Button
              icon={<BarChartOutlined />}
              onClick={handleExportAnalysisReport}
              loading={exportLoading}
              type="primary"
              style={{ background: '#1890ff', borderColor: '#1890ff' }}
            >
              导出分析报告
            </Button> */}
          </div>
          <Popconfirm
            title="确定要删除选中的记录吗？"
            onConfirm={handleDelete}
            okText="确定"
            cancelText="取消"
            disabled={selectedRowKeys.length === 0}
          >
            <Button
              type="primary"
              danger
              icon={<DeleteOutlined />}
              disabled={selectedRowKeys.length === 0}
              loading={deleteLoading}
            >
              删除选中记录 ({selectedRowKeys.length})
            </Button>
          </Popconfirm>
        </div>
      }
    >
      <div style={{ marginBottom: 16 }}>
        <Space>
          <span>时间范围：</span>
          <RangePicker
            showTime
            format="YYYY-MM-DD HH:mm:ss"
            value={dateRange as any}
            onChange={handleDateRangeChange}
            allowClear={false}
            style={{ width: 400 }}
          />
          <span>评分标准：</span>
          <Select
            placeholder="选择评分标准"
            style={{ width: 300 }}
            allowClear
            loading={loadingCriteria}
            value={selectedCriteriaId}
            onChange={handleCriteriaChange}
            options={criteriaList.map(item => ({
              label: item.content.length > 30 ? item.content.substring(0, 30) + '...' : item.content,
              value: item.id,
              title: item.content // 鼠标悬停时显示完整内容
            }))}
            showSearch
            optionFilterProp="title"
          />
          <Button
            type="primary"
            icon={<SearchOutlined />}
            onClick={selectedCriteriaId ? searchByTimeRangeAndCriteria : searchByDateRange}
            loading={searchLoading}
          >
            查询
          </Button>
        </Space>
      </div>
      <Table
        dataSource={records}
        columns={columns}
        rowKey="answer_image_hash"
        pagination={false}
        scroll={{ x: 1100, y: 400 }}
        onChange={handleTableChange}
        rowSelection={rowSelection}
        size="small"
        className="compact-table"
      />
      <div style={{ marginTop: 10, textAlign: "right" }}>
        <div>共 {records.length} 条记录</div>
      </div>
    </Modal>
  );
};

export default GradingRecordsDialog;
