import React, { useState, useEffect } from "react";
import { Modal, Form, Input, Button, message, Row, Col, InputNumber } from "antd";
import { LogError, LogInfo } from "../../wailsjs/runtime/runtime";
import { SaveGradingCriteriaStruct } from "../../wailsjs/go/main/App";
import { main } from "../../wailsjs/go/models";

interface GradingCriteriaDialogProps {
  open: boolean;
  onCancel: () => void;
  onSuccess: (criteria: main.GradingCriteriaStruct) => void;
  subject: string;
  structuredCriteria?: main.GradingCriteriaStruct;
}

const GradingCriteriaDialog: React.FC<GradingCriteriaDialogProps> = ({
  open,
  onCancel,
  onSuccess,
  subject,
  structuredCriteria
}) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);

  // 初始化表单数据
  useEffect(() => {
    if (open) {
      // 使用结构化数据
      if (structuredCriteria) {
        form.setFieldsValue(structuredCriteria);
        LogInfo("使用结构化评分标准初始化表单");
      }
    }
  }, [open, structuredCriteria, form]);

  const handleSubmit = async () => {
    try {
      setLoading(true);
      const values = await form.validateFields();
      // 使用新的结构化保存方法
      await SaveGradingCriteriaStruct(
        values as main.GradingCriteriaStruct,
        subject
      );
      onSuccess(values as main.GradingCriteriaStruct)
      LogInfo("保存结构化评分标准到配置文件");
      message.success("评分标准保存成功");
    } catch (err) {
      LogError(`保存评分标准失败: ${err}`);
      message.error("保存失败: " + (err as Error).message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Modal
      title="设置评分标准"
      open={open}
      onCancel={onCancel}
      width={570}
      footer={[
        <Button key="cancel" onClick={onCancel}>
          取消
        </Button>,
        <Button
          key="submit"
          type="primary"
          loading={loading}
          onClick={handleSubmit}
        >
          保存
        </Button>,
      ]}
    >
      <Form form={form} layout="vertical">
        <Form.Item
          name="default_criteria"
          label="评分标准"
          rules={[{ required: true, message: '请输入评分标准' }]}
        >
          <Input.TextArea
            showCount
            placeholder="请输入评分内容，详细描述问题和评分标准，这和评分准确率成正相关。"
            style={{ height: '160px', resize: 'none' }}
          />
        </Form.Item>

        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              name="scoring_points"
              label="补充得分点答案"
            >
              <Input.TextArea
                showCount
                placeholder="请输入得分点"
                style={{ height: '160px', resize: 'none' }}
              />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="deduction_points"
              label="不得分点答案"
            >
              <Input.TextArea
                showCount
                placeholder="请输入不得分点"
                style={{ height: '160px', resize: 'none' }}
              />
            </Form.Item>
          </Col>
        </Row>
        <Form.Item
          name="total_score"
          label="总分"
          rules={[{ required: true, message: '请输入总分' }]}
        >
          <InputNumber
            min={0}
            max={1000}
            placeholder="请输入总分"
            style={{ width: '100%' }}
          />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default GradingCriteriaDialog;
