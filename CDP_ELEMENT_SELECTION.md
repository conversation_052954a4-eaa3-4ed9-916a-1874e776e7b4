# CDP元素选择功能说明

## 概述

本项目已将元素选择功能从JavaScript注入方式升级为使用Chrome DevTools Protocol (CDP)实现。这种新方式提供了更好的兼容性和稳定性。

## 主要改进

### 1. 技术架构升级
- **原方式**: JavaScript注入 + DOM事件监听
- **新方式**: Chrome DevTools Protocol + Overlay API

### 2. 兼容性提升
- 解决了某些网站CSP（内容安全策略）阻止JavaScript注入的问题
- 提供与浏览器开发者工具一致的用户体验
- 支持更多复杂的网页结构

### 3. 功能增强
- 原生的元素高亮显示
- 更准确的元素选择
- 更好的错误处理和恢复机制

## 实现流程

```mermaid
sequenceDiagram
    participant User as 用户
    participant Wails as Wails前端
    participant Go as Go后端
    participant CDP as CDP会话
    participant Browser as 浏览器

    User->>Wails: 点击"选择元素"按钮
    Wails->>Go: 调用SelectElement()
    Go->>CDP: 创建CDP会话
    Go->>CDP: 启用DOM、Overlay、Runtime域
    Go->>CDP: 设置inspectMode为searchForNode
    Go->>Browser: 注入ESC键处理脚本
    Browser-->>User: 显示元素审查模式
    User->>Browser: 点击目标元素
    Browser->>CDP: 触发inspectNodeRequested事件
    CDP->>Go: 返回backendNodeId
    Go->>Go: 生成CSS选择器
    Go->>CDP: 禁用审查模式
    Go->>Wails: 返回选择器
    Wails-->>User: 显示选择结果
```

## 核心方法

### SelectElement()
主要的元素选择方法，使用CDP实现：

```go
func (a *App) SelectElement() (string, error)
```

**特性**:
- 使用CDP的Overlay.setInspectMode启用元素审查
- 监听Overlay.inspectNodeRequested事件
- 支持ESC键取消选择
- 60秒超时保护
- 自动资源清理

### generateSelectorFromNodeId()
根据CDP节点ID生成CSS选择器：

```go
func (a *App) generateSelectorFromNodeId(cdpSession playwright.CDPSession, backendNodeId interface{}) (string, error)
```

**选择器优先级**:
1. ID选择器 (`#id`)
2. Name属性选择器 (`[name="value"]`)
3. Data-testid属性选择器 (`[data-testid="value"]`)
4. 类名选择器 (`.class`)
5. 标签+属性组合选择器
6. 完整路径选择器

## 错误处理

### 1. 连接错误
- CDP会话创建失败
- 域启用失败
- 浏览器上下文不可用

### 2. 选择错误
- 节点信息获取失败
- 选择器生成失败
- 选择器验证失败

### 3. 超时处理
- 60秒无操作自动超时
- 自动清理审查模式
- 资源释放

### 4. 用户取消
- ESC键取消支持
- 视觉提示显示
- 优雅的状态恢复

## 使用方法

### 1. 基本使用
```go
// 在Go后端调用
selector, err := app.SelectElement()
if err != nil {
    // 处理错误
    log.Printf("选择元素失败: %v", err)
    return
}

if selector == "" {
    // 用户取消了选择
    log.Println("用户取消了元素选择")
    return
}

// 使用选择器
log.Printf("选择的元素: %s", selector)
```

### 2. 前端集成
```javascript
// 在Wails前端调用
import { SelectElement } from '../wailsjs/go/main/App';

async function selectElement() {
    try {
        const selector = await SelectElement();
        if (selector) {
            console.log('选择的元素:', selector);
            // 使用选择器进行后续操作
        } else {
            console.log('用户取消了选择');
        }
    } catch (error) {
        console.error('选择元素失败:', error);
    }
}
```

## 注意事项

### 1. 浏览器要求
- 仅支持基于Chromium的浏览器
- 需要启用CDP支持
- 要求浏览器版本支持Overlay API

### 2. 性能考虑
- CDP会话创建有一定开销
- 建议避免频繁调用
- 自动资源清理确保内存不泄漏

### 3. 安全性
- CDP需要特殊权限
- 仅在可信环境中使用
- 注意保护用户隐私

## 故障排除

### 1. "创建CDP会话失败"
- 检查浏览器是否支持CDP
- 确认浏览器上下文已正确初始化
- 验证Playwright版本兼容性

### 2. "启用域失败"
- 检查CDP连接状态
- 确认浏览器权限设置
- 重试连接或重启浏览器

### 3. "选择元素超时"
- 检查用户是否在60秒内操作
- 确认页面是否响应
- 检查网络连接状态

### 4. "生成选择器失败"
- 检查目标元素是否存在
- 确认元素属性完整性
- 尝试手动验证选择器

## 测试

运行测试：
```bash
go test -v ./element_selection_test.go
```

注意：集成测试需要实际的浏览器环境，请根据需要启用相应的测试用例。
