import React, { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON>, Button, Input, message } from "antd";
import { SubmitRechargeOrder } from "../../wailsjs/go/main/App";
import { useAuth } from "../contexts/AuthContext";
import credits1 from "../assets/images/qrcodes/credits_1.jpg";
import credits5 from "../assets/images/qrcodes/credits_5.jpg";
import credits10 from "../assets/images/qrcodes/credits_10.jpg";
import credits20 from "../assets/images/qrcodes/credits_20.jpg";
import credits50 from "../assets/images/qrcodes/credits_50.jpg";

interface RechargeDialogProps {
  isOpen: boolean;
  onClose: () => void;
}

const RechargeDialog: React.FC<RechargeDialogProps> = ({ isOpen, onClose }) => {
  const handleAfterClose = () => {
    setOrderNumber("");
  };
  const { isAuthenticated, username } = useAuth();
  const [orderNumber, setOrderNumber] = useState("");
  const [showConfirmModal, setShowConfirmModal] = useState(false);

  const rechargeAmounts = [
    { amount: 1, label: "1元", image: credits1 },
    { amount: 5, label: "5元", image: credits5 },
    { amount: 10, label: "10元", image: credits10 },
    { amount: 20, label: "20元", image: credits20 },
    { amount: 50, label: "50元", image: credits50 },
  ];

  const [selectedAmount, setSelectedAmount] = useState<number | null>(rechargeAmounts[0].amount);
  const [loading, setLoading] = useState(false);

  const handleSubmit = async () => {
    if (!isAuthenticated) {
      message.error("请先登录后再进行充值");
      onClose();
      return;
    }

    if (!orderNumber || !selectedAmount) {
      message.warning("请选择充值金额并输入订单号");
      return;
    }

    try {
      setLoading(true);
      const response = await SubmitRechargeOrder(orderNumber, selectedAmount);
      setLoading(false);

      if (response.success) {
        message.success(response.message || "充值订单提交成功");
        setShowConfirmModal(true);
      } else {
        message.error(response.message || "充值失败");
        // 如果是未登录错误，关闭对话框
        if (response.message && response.message.includes("未登录")) {
          onClose();
        }
      }
    } catch (error) {
      setLoading(false);
      message.error("提交订单失败，请稍后重试");
    }
  };

  const handleTabChange = (key: string) => {
    setSelectedAmount(Number(key));
  };

  return (
    <>
      <Modal
        title="充值提交成功"
        open={showConfirmModal}
        footer={null}
        onCancel={() => setShowConfirmModal(false)}
      >
        <p>请等待后台审核</p>
        <p style={{ color: '#4A5568', marginBottom: '5px' }}>系统将发送确认邮件到您的注册邮箱，收到成功邮件后，请双击余额或重新登录刷新余额</p>
        <Button type="primary" onClick={() => {
          setShowConfirmModal(false);
          onClose();
        }}>确认</Button>
      </Modal>

      <Modal
        title="充值"
        open={isOpen}
        onCancel={onClose}
        afterClose={handleAfterClose}
        footer={null}
        width={350}
        maskClosable={false}
      >
        {isAuthenticated ? (
          <>
            <p style={{ marginBottom: '2px', color: '#4A5568' }}>当前账号: {username || "未知用户"}</p>
            <Tabs onChange={handleTabChange} style={{ marginTop: '2px' }}>
              {rechargeAmounts.map((item) => (
                <Tabs.TabPane tab={item.label} key={item.amount.toString()}>
                  <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'center', padding: '2px' }}>
                    <div style={{ width: '200px', height: '200px', backgroundColor: '#F7FAFC', marginBottom: '2px', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                      <img
                        src={item.image}
                        alt={`${item.amount}元充值二维码`}
                        style={{ maxWidth: '190px', maxHeight: '190px', objectFit: 'contain' }}
                      />
                    </div>
                    {/* <p style={{ fontSize: '13px', color: '#4A5568', marginBottom: '2px' }}>充值{item.amount}元可获得{item.amount * 1000}积分</p> */}
                    <p style={{ fontSize: '15px', color: '#A0AEC0', marginBottom: '1px' }}>请使用支付宝扫描上方二维码完成支付</p>
                    <p style={{ fontSize: '20px', color: '#FF0000', marginBottom: '1px' }}>支付宝付款后，请将支付宝订单号填写到下方输入框，然后点击提交</p>
                  </div>
                </Tabs.TabPane>
              ))}
            </Tabs>
            <div style={{ marginTop: '2px' }}>
              <label htmlFor="orderNumber" style={{ display: 'block', fontSize: '14px', fontWeight: '500', color: '#4A5568', marginBottom: '4px' }}>
                支付宝订单号
              </label>
              <Input
                id="orderNumber"
                value={orderNumber}
                onChange={(e) => setOrderNumber(e.target.value)}
                placeholder="请输入支付宝订单号"
                style={{ marginBottom: '2px' }}
              />
            </div>

            <div style={{ marginTop: '5px', display: 'flex', justifyContent: 'flex-end' }}>
              <Button type="primary" onClick={handleSubmit} loading={loading}>
                提交
              </Button>
            </div>
          </>
        ) : (
          <div style={{ padding: '10px 0', textAlign: 'center' }}>
            <p style={{ color: '#F56565', marginBottom: '5px' }}>您需要先登录才能进行充值操作</p>
            <Button type="primary" onClick={onClose}>
              确定
            </Button>
          </div>
        )}
      </Modal>
    </>
  );
};

export default RechargeDialog;