html {
  background-color: #f0f2f5;
}

body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Oxygen",
    "Ubuntu", "Cantarell", "Fira Sans", "Droid Sans", "Helvetica Neue",
    sans-serif;
}

@font-face {
  font-family: "Nunito";
  font-style: normal;
  font-weight: 400;
  src: local(""),
    url("assets/fonts/nunito-v16-latin-regular.woff2") format("woff2");
}

#app {
  height: 100vh;
  text-align: center;
}

/* Compact table styles */
.compact-table .ant-table-cell {
  padding: 4px 8px !important;
}

.compact-table .ant-table-thead > tr > th {
  padding: 6px 8px !important;
}

.compact-table .ant-typography {
  margin-bottom: 0 !important;
}
