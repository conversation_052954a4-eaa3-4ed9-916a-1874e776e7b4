package main

import (
	"testing"
	"time"
)

// TestSelectElementCDP 测试基于CDP的元素选择功能
func TestSelectElementCDP(t *testing.T) {
	// 这是一个集成测试，需要实际的浏览器环境
	// 在实际使用中，需要先调用OpenURL打开一个页面
	
	t.Skip("这是一个集成测试，需要手动运行")
	
	// 创建App实例
	app := NewApp()
	
	// 模拟打开一个测试页面
	err := app.OpenURL("custom")
	if err != nil {
		t.Fatalf("打开页面失败: %v", err)
	}
	
	// 导航到测试页面
	if app.page != nil {
		_, err = app.page.Goto("data:text/html,<html><body><div id='test'>Test Element</div><button class='btn'>Click Me</button></body></html>")
		if err != nil {
			t.Fatalf("导航到测试页面失败: %v", err)
		}
	}
	
	// 测试元素选择
	// 注意：这需要用户手动在浏览器中选择元素
	selector, err := app.SelectElement()
	if err != nil {
		t.Fatalf("选择元素失败: %v", err)
	}
	
	if selector == "" {
		t.Log("用户取消了元素选择")
	} else {
		t.Logf("成功选择元素，选择器: %s", selector)
	}
}

// TestSelectElementTimeout 测试超时机制
func TestSelectElementTimeout(t *testing.T) {
	t.Skip("这是一个集成测试，需要手动运行")
	
	app := NewApp()
	
	// 模拟超时情况
	// 在实际测试中，可以修改超时时间为更短的值
	start := time.Now()
	_, err := app.SelectElement()
	duration := time.Since(start)
	
	if err != nil && err.Error() == "选择元素超时" {
		t.Logf("超时测试通过，耗时: %v", duration)
	} else {
		t.Errorf("期望超时错误，但得到: %v", err)
	}
}

// TestSelectorGeneration 测试选择器生成逻辑
func TestSelectorGeneration(t *testing.T) {
	app := NewApp()
	
	// 测试节点选择器生成
	testCases := []struct {
		name     string
		node     map[string]interface{}
		expected string
	}{
		{
			name: "带ID的元素",
			node: map[string]interface{}{
				"nodeName":   "DIV",
				"attributes": []interface{}{"id", "test-id", "class", "container"},
			},
			expected: "#test-id",
		},
		{
			name: "带类名的元素",
			node: map[string]interface{}{
				"nodeName":   "BUTTON",
				"attributes": []interface{}{"class", "btn primary", "type", "button"},
			},
			expected: "button.btn.primary",
		},
		{
			name: "输入元素",
			node: map[string]interface{}{
				"nodeName":   "INPUT",
				"attributes": []interface{}{"type", "text", "name", "username"},
			},
			expected: "input[type=\"text\"]",
		},
	}
	
	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			result := app.generateNodeSelector(tc.node)
			if result != tc.expected {
				t.Errorf("期望选择器 %s，但得到 %s", tc.expected, result)
			}
		})
	}
}

// BenchmarkSelectorGeneration 性能测试
func BenchmarkSelectorGeneration(b *testing.B) {
	app := NewApp()
	
	node := map[string]interface{}{
		"nodeName":   "DIV",
		"attributes": []interface{}{"class", "container main", "data-id", "123"},
	}
	
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		app.generateNodeSelector(node)
	}
}
