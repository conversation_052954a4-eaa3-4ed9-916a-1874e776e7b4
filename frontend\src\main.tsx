import React from 'react'
import {createRoot} from 'react-dom/client'
import { ConfigProvider } from 'antd'
import './style.css'
import App from './App'
import { LoadingProvider } from './contexts/LoadingContext'
import { AuthProvider } from './contexts/AuthContext'
import theme from './theme'

const container = document.getElementById('root')

const root = createRoot(container!)

root.render(
    <React.StrictMode>
        <ConfigProvider theme={theme}>
            <LoadingProvider>
                <AuthProvider>
                    <App/>
                </AuthProvider>
            </LoadingProvider>
        </ConfigProvider>
    </React.StrictMode>
)
