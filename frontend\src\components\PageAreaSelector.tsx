import React, { useState, useRef, useEffect } from 'react';
import { Button, Space, Tooltip, Modal, Spin } from 'antd';
import { CameraOutlined, CloseOutlined } from '@ant-design/icons';
import { LogInfo, LogError } from '../../wailsjs/runtime/runtime';

interface PageAreaSelectorProps {
  visible: boolean;
  onClose: () => void;
  onCapture: (x: number, y: number, width: number, height: number) => void;
}

interface SelectionArea {
  startX: number;
  startY: number;
  endX: number;
  endY: number;
  width: number;
  height: number;
}

const PageAreaSelector: React.FC<PageAreaSelectorProps> = ({ visible, onClose, onCapture }) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const imageRef = useRef<HTMLImageElement>(null);
  const [isSelecting, setIsSelecting] = useState(false);
  const [selectionArea, setSelectionArea] = useState<SelectionArea | null>(null);
  const [imageLoaded, setImageLoaded] = useState(false);
  const [imageSize, setImageSize] = useState({ width: 0, height: 0 });
  const [imageData, setImageData] = useState<string>("");
  const [loading, setLoading] = useState(false);

  // 当对话框打开时获取页面截图
  useEffect(() => {
    if (visible) {
      captureFullPage();
    } else {
      // 重置状态
      setSelectionArea(null);
      setImageData("");
      setImageLoaded(false);
    }
  }, [visible]);

  // 获取整个页面的截图
  const captureFullPage = async () => {
    setLoading(true);
    try {
      // 这里需要调用后端的截图功能，获取整个页面的截图
      // 假设有一个名为 CaptureFullPage 的后端函数
      const { CaptureFullPage } = await import("../../wailsjs/go/main/App");
      const dataUrl = await CaptureFullPage();
      setImageData(dataUrl);
    } catch (err) {
      LogError(`获取页面截图失败: ${err}`);
    } finally {
      setLoading(false);
    }
  };

  // 处理图片加载完成事件
  const handleImageLoad = () => {
    if (imageRef.current) {
      setImageLoaded(true);
      setImageSize({
        width: imageRef.current.naturalWidth,
        height: imageRef.current.naturalHeight
      });
    }
  };

  // 处理鼠标按下事件，开始选择区域
  const handleMouseDown = (e: React.MouseEvent<HTMLDivElement>) => {
    if (!containerRef.current || !imageRef.current || !imageLoaded) return;

    // 阻止默认行为，防止图片被拖动
    e.preventDefault();

    // 获取图片相对于容器的位置
    const rect = imageRef.current.getBoundingClientRect();

    // 计算鼠标在图片上的位置
    const x = e.clientX - rect.left;
    const y = e.clientY - rect.top;

    // 确保点击在图片内部
    if (x >= 0 && x <= rect.width && y >= 0 && y <= rect.height) {
      setIsSelecting(true);
      setSelectionArea({
        startX: x,
        startY: y,
        endX: x,
        endY: y,
        width: 0,
        height: 0
      });
    }
  };

  // 处理鼠标移动事件，更新选择区域
  const handleMouseMove = (e: React.MouseEvent<HTMLDivElement>) => {
    if (!isSelecting || !selectionArea || !containerRef.current || !imageRef.current) return;

    // 阻止默认行为，防止图片被拖动
    e.preventDefault();

    // 获取图片相对于容器的位置
    const rect = imageRef.current.getBoundingClientRect();

    // 计算鼠标在图片上的位置
    let endX = Math.max(0, Math.min(e.clientX - rect.left, rect.width));
    let endY = Math.max(0, Math.min(e.clientY - rect.top, rect.height));

    // 计算选择区域的宽度和高度
    const width = Math.abs(endX - selectionArea.startX);
    const height = Math.abs(endY - selectionArea.startY);

    setSelectionArea({
      ...selectionArea,
      endX,
      endY,
      width,
      height
    });
  };

  // 处理鼠标松开事件，完成选择区域
  const handleMouseUp = (e: React.MouseEvent<HTMLDivElement>) => {
    // 阻止默认行为，防止图片被拖动
    e.preventDefault();

    if (isSelecting) {
      setIsSelecting(false);
    }
  };

  // 处理截图按钮点击事件
  const handleCapture = () => {
    if (!selectionArea || !imageRef.current) return;

    // 计算实际的选择区域（考虑图片的缩放）
    const rect = imageRef.current.getBoundingClientRect();
    const scaleX = imageSize.width / rect.width;
    const scaleY = imageSize.height / rect.height;

    // 计算选择区域的左上角坐标
    const x = Math.min(selectionArea.startX, selectionArea.endX) * scaleX;
    const y = Math.min(selectionArea.startY, selectionArea.endY) * scaleY;

    // 计算选择区域的宽度和高度
    const width = selectionArea.width * scaleX;
    const height = selectionArea.height * scaleY;

    LogInfo(`截取区域: x=${x}, y=${y}, width=${width}, height=${height}`);

    // 调用回调函数，传递选择区域的坐标和尺寸
    onCapture(Math.round(x), Math.round(y), Math.round(width), Math.round(height));
    onClose();
  };

  // 计算选择区域的样式
  const getSelectionStyle = () => {
    if (!selectionArea) return {};

    const left = Math.min(selectionArea.startX, selectionArea.endX);
    const top = Math.min(selectionArea.startY, selectionArea.endY);

    return {
      left: `${left}px`,
      top: `${top}px`,
      width: `${selectionArea.width}px`,
      height: `${selectionArea.height}px`
    };
  };

  return (
    <Modal
      title="选择截图区域"
      open={visible}
      onCancel={onClose}
      footer={null}
      width={800}
      centered
    >
      {loading ? (
        <div style={{ textAlign: 'center', padding: '40px 0' }}>
          <Spin tip="正在获取页面截图..." />
        </div>
      ) : (
        <div className="page-area-selector-container">
          <div
            ref={containerRef}
            className="image-container"
            onMouseDown={handleMouseDown}
            onMouseMove={handleMouseMove}
            onMouseUp={handleMouseUp}
            onMouseLeave={handleMouseUp}
            style={{
              position: 'relative',
              cursor: isSelecting ? 'crosshair' : 'default',
              userSelect: 'none',
              WebkitUserSelect: 'none',
              MozUserSelect: 'none',
              msUserSelect: 'none'
            }}
          >
            {imageData ? (
              <img
                ref={imageRef}
                src={imageData}
                alt="Page Screenshot"
                style={{ maxWidth: '100%', display: 'block', userSelect: 'none' }}
                onLoad={handleImageLoad}
                draggable="false"
              />
            ) : (
              <div style={{ textAlign: 'center', padding: '40px 0' }}>
                无法获取页面截图
              </div>
            )}
            {selectionArea && selectionArea.width > 0 && selectionArea.height > 0 && (
              <div
                className="selection-area"
                style={{
                  position: 'absolute',
                  border: '2px dashed #1890ff',
                  backgroundColor: 'rgba(24, 144, 255, 0.1)',
                  pointerEvents: 'none',
                  ...getSelectionStyle()
                }}
              />
            )}
          </div>

          <Space style={{ marginTop: 10, display: 'flex', justifyContent: 'center' }}>
            <Tooltip title="截取选中区域">
              <Button
                type="primary"
                icon={<CameraOutlined />}
                onClick={handleCapture}
                disabled={!selectionArea || selectionArea.width < 5 || selectionArea.height < 5}
              >
                截取选中区域
              </Button>
            </Tooltip>
            <Button icon={<CloseOutlined />} onClick={onClose}>取消</Button>
          </Space>

          <div style={{ marginTop: 5, fontSize: 12, color: '#888', textAlign: 'center' }}>
            提示: 在图片上按住鼠标左键并拖动来选择区域
          </div>
        </div>
      )}
    </Modal>
  );
};

export default PageAreaSelector;
