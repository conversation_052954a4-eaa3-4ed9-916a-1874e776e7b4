import React, { useState, useRef, useEffect } from 'react';
import { Button, Space, Tooltip, Modal, Spin, List, Tag, Input, Radio, message } from 'antd';
import { CloseOutlined, DeleteOutlined, PlusOutlined, SelectOutlined } from '@ant-design/icons';
import { LogInfo, LogError } from '../../wailsjs/runtime/runtime';

interface ElementSelectorProps {
  visible: boolean;
  onClose: () => void;
  onSave: (elements: ElementInfo[]) => void;
}

export interface ElementInfo {
  selector: string;
  description: string;
  type: 'click' | 'input';
  value?: string; // 仅在 type 为 'input' 时使用
}

const ElementSelector: React.FC<ElementSelectorProps> = ({ visible, onClose, onSave }) => {
  const [loading, setLoading] = useState(false);
  const [imageData, setImageData] = useState<string>("");
  const [selectedElements, setSelectedElements] = useState<ElementInfo[]>([]);
  const [isSelecting, setIsSelecting] = useState(false);
  const [currentElement, setCurrentElement] = useState<ElementInfo | null>(null);
  const [elementType, setElementType] = useState<'click' | 'input'>('click');

  // 当对话框打开时获取页面截图
  useEffect(() => {
    if (visible) {
      captureFullPage();
    } else {
      // 重置状态
      setImageData("");
    }
  }, [visible]);

  // 获取整个页面的截图
  const captureFullPage = async () => {
    setLoading(true);
    try {
      // 调用后端的截图功能，获取整个页面的截图
      const { CaptureFullPage } = await import("../../wailsjs/go/main/App");
      const dataUrl = await CaptureFullPage();
      setImageData(dataUrl);
    } catch (err) {
      LogError(`获取页面截图失败: ${err}`);
    } finally {
      setLoading(false);
    }
  };

  // 开始选择元素
  const startElementSelection = () => {
    setIsSelecting(true);
    // 这里需要调用后端的函数，让用户选择元素
    selectElement();
  };

  // 选择元素
  const selectElement = async () => {
    try {
      // 调用后端的函数，让用户选择元素
      const { SelectElement } = await import("../../wailsjs/go/main/App");
      const selector = await SelectElement();

      if (selector) {
        // 创建新的元素信息
        const newElement: ElementInfo = {
          selector,
          description: `元素 ${selectedElements.length + 1}`,
          type: elementType,
          value: elementType === 'input' ? "评分结果将在此处填入" : undefined
        };

        setCurrentElement(newElement);
      }
    } catch (err) {
      LogError(`选择元素失败: ${err}`);
    } finally {
      setIsSelecting(false);
    }
  };

  // 添加当前选择的元素到列表
  const addCurrentElement = () => {
    if (currentElement) {
      setSelectedElements([...selectedElements, currentElement]);
      setCurrentElement(null);
    }
  };

  // 删除元素
  const removeElement = (index: number) => {
    const newElements = [...selectedElements];
    newElements.splice(index, 1);
    setSelectedElements(newElements);
  };

  // 保存选择的元素
  const handleSave = () => {
    // 检查是否只有一个输入操作
    const inputElements = selectedElements.filter(el => el.type === 'input');
    if (inputElements.length === 0) {
      message.warning("请至少添加一个输入操作元素，用于填写评分结果");
      return;
    }

    if (inputElements.length > 1) {
      message.warning("只能有一个输入操作元素，请删除多余的输入操作");
      return;
    }

    onSave(selectedElements);
    onClose();
  };

  return (
    <Modal
      title="选择网页元素"
      open={visible}
      onCancel={onClose}
      maskClosable={false}
      width={800}
      footer={[
        <Button key="cancel" onClick={onClose}>取消</Button>,
        <Button
          key="save"
          type="primary"
          onClick={handleSave}
          disabled={selectedElements.length === 0}
        >
          保存
        </Button>
      ]}
    >
      {loading ? (
        <div style={{ textAlign: 'center', padding: '40px 0' }}>
          <Spin tip="正在获取页面信息..." />
        </div>
      ) : (
        <div>
          <div style={{ marginBottom: 16 }}>
            <Space>
              <Radio.Group
                value={elementType}
                onChange={(e) => setElementType(e.target.value)}
                disabled={isSelecting}
              >
                <Radio.Button value="click">点击操作</Radio.Button>
                <Radio.Button value="input">输入操作</Radio.Button>
              </Radio.Group>

              <Button
                type="primary"
                icon={<SelectOutlined />}
                onClick={startElementSelection}
                loading={isSelecting}
              >
                {isSelecting ? "选择中..." : "选择元素"}
              </Button>
            </Space>
          </div>

          {currentElement && (
            <div style={{ marginBottom: 16, padding: 10, border: '1px dashed #d9d9d9', borderRadius: 4 }}>
              <div style={{ marginBottom: 8 }}>
                <strong>当前选择的元素:</strong>
              </div>
              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <div>
                  <div><strong>选择器:</strong> {currentElement.selector}</div>
                  <div><strong>描述:</strong> {currentElement.description}</div>
                  <div>
                    <strong>操作类型:</strong> {currentElement.type === 'click' ? '点击' : '输入'}
                    {currentElement.type === 'input' && currentElement.value && (
                      <span> (值: {currentElement.value})</span>
                    )}
                  </div>
                </div>
                <Button type="primary" onClick={addCurrentElement}>添加到列表</Button>
              </div>
            </div>
          )}

          <div style={{ marginBottom: 16 }}>
            <h3>已选择的元素 ({selectedElements.length})</h3>
            <List
              bordered
              dataSource={selectedElements}
              renderItem={(item, index) => (
                <List.Item
                  actions={[
                    <Button
                      key="delete"
                      type="text"
                      danger
                      icon={<DeleteOutlined />}
                      onClick={() => removeElement(index)}
                    />
                  ]}
                >
                  <List.Item.Meta
                    title={
                      <div>
                        {item.description}
                        <Tag color={item.type === 'click' ? 'blue' : 'green'} style={{ marginLeft: 8 }}>
                          {item.type === 'click' ? '点击' : '输入'}
                        </Tag>
                      </div>
                    }
                    description={
                      <div>
                        <div><strong>选择器:</strong> {item.selector}</div>
                        {item.type === 'input' && item.value && (
                          <div><strong>输入值:</strong> {item.value}</div>
                        )}
                      </div>
                    }
                  />
                </List.Item>
              )}
              locale={{ emptyText: "暂无选择的元素，请点击‘选择元素’按钮添加" }}
            />
          </div>

          <div style={{ marginTop: 16 }}>
            <p>
              <strong>提示:</strong> 请添加一个输入操作元素（用于填写评分结果）和若干点击操作元素。
              选择元素时，请确保页面上的元素可见。
            </p>
          </div>
        </div>
      )}
    </Modal>
  );
};

export default ElementSelector;
