package main

import (
	"context"
	"fmt"
	"os"
	"path/filepath"
	"sync"
	"time"

	"github.com/adrg/xdg"
	"github.com/wailsapp/wails/v2/pkg/runtime"
	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
)

// Logger 是一个用于处理日志的结构体，使用zap作为底层实现
type Logger struct {
	ctx         context.Context
	zapLogger   *zap.Logger
	sugarLogger *zap.SugaredLogger
	mutex       sync.Mutex
}

// 全局logger实例
var AppLogger *Logger

// 确保日志目录存在
func ensureLogDir() string {
	logDir := filepath.Join(xdg.ConfigHome, "ai-grading", "logs")
	if err := os.MkdirAll(logDir, 0o755); err != nil {
		fmt.Printf("无法创建日志目录: %v\n", err)
	} else {
		fmt.Printf("日志目录创建成功: %s\n", logDir)
	}
	return logDir
}

// 创建一个新的zap logger实例
func newZapLogger() (*zap.Logger, error) {
	// 确保日志目录存在
	logDir := ensureLogDir()
	logFilePath := filepath.Join(logDir, fmt.Sprintf("ai-grading-%s.log", time.Now().Format("2006-01-02")))

	// 创建编码器配置
	encoderConfig := zap.NewProductionEncoderConfig()
	encoderConfig.EncodeTime = zapcore.ISO8601TimeEncoder
	encoderConfig.EncodeLevel = zapcore.CapitalLevelEncoder

	// 创建JSON编码器用于文件输出
	jsonEncoder := zapcore.NewJSONEncoder(encoderConfig)
	// 创建控制台编码器用于标准输出
	consoleEncoder := zapcore.NewConsoleEncoder(encoderConfig)

	// 打开日志文件
	logFile, err := os.OpenFile(logFilePath, os.O_APPEND|os.O_CREATE|os.O_WRONLY, 0o644)
	if err != nil {
		return nil, fmt.Errorf("无法打开日志文件: %v", err)
	}

	// 创建核心，同时输出到文件和标准输出
	core := zapcore.NewTee(
		zapcore.NewCore(jsonEncoder, zapcore.AddSync(logFile), zapcore.InfoLevel),
		zapcore.NewCore(consoleEncoder, zapcore.AddSync(os.Stdout), zapcore.InfoLevel),
	)

	// 创建logger
	logger := zap.New(core, zap.AddCaller(), zap.AddCallerSkip(1))

	return logger, nil
}

// NewLogger 创建一个新的Logger实例
func NewLogger(ctx context.Context) *Logger {
	zapLogger, err := newZapLogger()
	if err != nil {
		fmt.Printf("初始化zap日志器失败: %v\n", err)
		// 如果初始化失败，创建一个空的logger
		zapLogger, _ = zap.NewProduction()
	}

	return &Logger{
		ctx:         ctx,
		zapLogger:   zapLogger,
		sugarLogger: zapLogger.Sugar(),
		mutex:       sync.Mutex{},
	}
}

// LogToFrontend 将日志发送到前端
func (l *Logger) LogToFrontend(message string, level string) {
	if l.ctx == nil {
		return
	}

	// 根据日志级别发送不同的事件
	switch level {
	case "info":
		runtime.EventsEmit(l.ctx, "log:info", message)
	case "error":
		runtime.EventsEmit(l.ctx, "log:error", message)
	case "warning":
		runtime.EventsEmit(l.ctx, "log:warning", message)
	case "debug":
		runtime.EventsEmit(l.ctx, "log:debug", message)
	case "trace":
		runtime.EventsEmit(l.ctx, "log:trace", message)
	default:
		// 默认为info级别
		runtime.EventsEmit(l.ctx, "log:info", message)
	}
}

// Info 记录info级别的日志
func (l *Logger) Info(format string, args ...any) {
	l.mutex.Lock()
	defer l.mutex.Unlock()

	message := fmt.Sprintf(format, args...)
	l.sugarLogger.Info(message)
	// l.LogToFrontend(message, "info")
}

// Error 记录error级别的日志
func (l *Logger) Error(format string, args ...any) {
	l.mutex.Lock()
	defer l.mutex.Unlock()

	message := fmt.Sprintf(format, args...)
	l.sugarLogger.Error(message)
	// l.LogToFrontend(message, "error")
}

// Warning 记录warning级别的日志
func (l *Logger) Warning(format string, args ...any) {
	l.mutex.Lock()
	defer l.mutex.Unlock()

	message := fmt.Sprintf(format, args...)
	l.sugarLogger.Warn(message)
	// l.LogToFrontend(message, "warning")
}

// Debug 记录debug级别的日志
func (l *Logger) Debug(format string, args ...any) {
	l.mutex.Lock()
	defer l.mutex.Unlock()

	message := fmt.Sprintf(format, args...)
	l.sugarLogger.Debug(message)
	// l.LogToFrontend(message, "debug")
}

// Trace 记录trace级别的日志
func (l *Logger) Trace(format string, args ...any) {
	l.mutex.Lock()
	defer l.mutex.Unlock()

	message := fmt.Sprintf(format, args...)
	// Zap没有trace级别，使用debug级别代替
	l.sugarLogger.Debug("[TRACE] " + message)
	// l.LogToFrontend(message, "trace")
}

// Close 关闭日志器，确保所有日志都被写入
func (l *Logger) Close() {
	if l.zapLogger != nil {
		_ = l.zapLogger.Sync()
	}
}

// InitLogger 初始化全局logger
func InitLogger(ctx context.Context) {
	fmt.Println("正在初始化日志系统...")

	// 确保日志目录存在
	logDir := ensureLogDir()
	fmt.Printf("日志将保存到: %s\n", logDir)

	AppLogger = NewLogger(ctx)

	if AppLogger != nil && AppLogger.zapLogger != nil {
		fmt.Println("日志系统初始化成功")
		AppLogger.Info("日志系统初始化成功")
	} else {
		fmt.Println("日志系统初始化失败")
	}

	// 在应用退出时确保日志被正确写入
	runtime.EventsOn(ctx, "app:beforeClose", func(optionalData ...any) {
		if AppLogger != nil {
			fmt.Println("应用正在关闭，同步日志...")
			AppLogger.Info("应用正在关闭，同步日志...")
			AppLogger.Close()
		}
	})
}
