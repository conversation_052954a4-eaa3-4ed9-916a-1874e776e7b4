/**
 * 版本比较工具函数
 * 支持格式：v0.0.8、v0.0.8.1、0.0.8 等
 * 只比较前三位版本号（主版本号.次版本号.修订号）
 */

/**
 * 解析版本号字符串，提取前三位版本号
 * @param version 版本号字符串，如 "v0.0.8" 或 "v0.0.8.1"
 * @returns 包含三位版本号的数组 [major, minor, patch]
 */
export function parseVersion(version: string): [number, number, number] {
  // 移除 'v' 前缀（如果存在）
  const cleanVersion = version.replace(/^v/, '');
  
  // 按点分割版本号
  const parts = cleanVersion.split('.');
  
  // 确保至少有三个部分，不足的用 0 填充
  const major = parseInt(parts[0] || '0', 10) || 0;
  const minor = parseInt(parts[1] || '0', 10) || 0;
  const patch = parseInt(parts[2] || '0', 10) || 0;
  
  return [major, minor, patch];
}

/**
 * 比较两个版本号的前三位
 * @param version1 版本号1
 * @param version2 版本号2
 * @returns 
 *  - 1: version1 > version2
 *  - 0: version1 = version2 (前三位相同)
 *  - -1: version1 < version2
 */
export function compareVersions(version1: string, version2: string): number {
  const [major1, minor1, patch1] = parseVersion(version1);
  const [major2, minor2, patch2] = parseVersion(version2);
  
  // 比较主版本号
  if (major1 > major2) return 1;
  if (major1 < major2) return -1;
  
  // 主版本号相同，比较次版本号
  if (minor1 > minor2) return 1;
  if (minor1 < minor2) return -1;
  
  // 前两位相同，比较修订号
  if (patch1 > patch2) return 1;
  if (patch1 < patch2) return -1;
  
  // 前三位完全相同
  return 0;
}

/**
 * 检查是否有新版本（远程版本是否大于当前版本）
 * @param currentVersion 当前版本
 * @param remoteVersion 远程版本
 * @returns true 表示有新版本，false 表示没有新版本
 */
export function hasNewVersion(currentVersion: string, remoteVersion: string): boolean {
  return compareVersions(remoteVersion, currentVersion) > 0;
}

/**
 * 检查两个版本的前三位是否相同
 * @param version1 版本号1
 * @param version2 版本号2
 * @returns true 表示前三位相同，false 表示不同
 */
export function isSameVersion(version1: string, version2: string): boolean {
  return compareVersions(version1, version2) === 0;
}