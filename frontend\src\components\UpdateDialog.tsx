import React from "react";
import { Mo<PERSON>, Typo<PERSON>, Button, Space } from "antd";
import { BrowserOpenURL } from "../../wailsjs/runtime";

const { Title, Paragraph, Text } = Typography;

interface UpdateDialogProps {
  visible: boolean;
  onClose: () => void;
  versionInfo: {
    version: string;
    downloadUrl: string;
    forceUpdate: boolean;
    updateLog: string;
  };
  currentVersion: string;
}

const UpdateDialog: React.FC<UpdateDialogProps> = ({
  visible,
  onClose,
  versionInfo,
  currentVersion,
}) => {
  const handleDownload = () => {
    // 使用Wails的runtime.BrowserOpenURL打开下载链接
    BrowserOpenURL(versionInfo.downloadUrl)
  };

  return (
    <Modal
      title="软件更新"
      open={visible}
      onCancel={versionInfo.forceUpdate ? undefined : onClose}
      footer={[
        !versionInfo.forceUpdate && (
          <Button key="cancel" onClick={onClose}>
            稍后更新
          </Button>
        ),
        <Button key="download" type="primary" onClick={handleDownload}>
          立即更新
        </Button>,
      ]}
      closable={!versionInfo.forceUpdate}
      maskClosable={!versionInfo.forceUpdate}
      width={550}
      centered
    >
      <Space direction="vertical" style={{ width: "100%" }}>
        <Title level={4}>发现新版本</Title>
        <Paragraph>
          <Text strong>当前版本：</Text> {currentVersion}
        </Paragraph>
        <Paragraph>
          <Text strong>最新版本：</Text> {versionInfo.version}
        </Paragraph>
        {versionInfo.forceUpdate && (
          <Paragraph type="danger">
            当前版本已停止支持，请立即更新到最新版本！
          </Paragraph>
        )}
        <Paragraph>
          <Text strong>更新内容：</Text>
        </Paragraph>
        <div
          style={{
            maxHeight: "200px",
            overflow: "auto",
            border: "1px solid #f0f0f0",
            padding: "10px",
            borderRadius: "4px",
            backgroundColor: "#fafafa",
          }}
        >
          {versionInfo.updateLog.split('\\n').map((line, i) => (
            <p key={i} className="py-1">{line}</p>
          ))}
        </div>
      </Space>
    </Modal>
  );
};

export default UpdateDialog;
