package main

import (
	"crypto/aes"
	"crypto/cipher"
	"crypto/rand"
	"encoding/base64"
	"errors"
	"io"
	"log"
	"os"
	"path/filepath"

	"github.com/adrg/xdg"
	"github.com/spf13/viper"
)

// 全局配置实例
var config *viper.Viper

func initConfig() {
	// 获取XDG配置目录
	configDir := filepath.Join(xdg.ConfigHome, "ai-grading")
	configPath := filepath.Join(configDir, "config.yaml")
	log.Printf("配置文件路径: %s", configPath)

	// 初始化全局配置
	config = viper.New()
	config.SetConfigName("config")
	config.SetConfigType("yaml")
	config.AddConfigPath(configDir)

	// 尝试读取配置
	if err := config.ReadInConfig(); err != nil {
		if _, ok := err.(viper.ConfigFileNotFoundError); ok {
			// 配置文件不存在，创建默认配置
			config.SetDefault("username", "")
			config.SetDefault("password", "")

			// 确保配置目录存在
			if err = os.MkdirAll(configDir, 0o755); err != nil {
				log.Fatalf("无法创建配置目录: %v", err)
			}

			// 写入默认配置
			if err = config.SafeWriteConfigAs(configPath); err != nil {
				log.Fatalf("无法写入默认配置: %v", err)
			}
		} else {
			// 其他错误
			log.Fatalf("读取配置文件错误: %v", err)
		}
	}
}

// GetConfig 获取全局配置实例
func GetConfig() *viper.Viper {
	return config
}

// GetDBPath 获取数据库文件路径
func GetDBPath() string {
	return filepath.Join(xdg.ConfigHome, "ai-grading", "grading.db")
}

// GetBrowserUserDataDir 获取浏览器用户数据目录
func GetBrowserUserDataDir() string {
	return filepath.Join(xdg.ConfigHome, "ai-grading", "msedge-user-data")
}

// 加密密钥(生产环境应从安全位置获取)
var encryptionKey = []byte("lQ5v1V6PBM3lYqGw")

// Encrypt 加密字符串(GCM模式)
func Encrypt(text string) (string, error) {
	plaintext := []byte(text)
	block, err := aes.NewCipher(encryptionKey)
	if err != nil {
		return "", err
	}

	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return "", err
	}

	nonce := make([]byte, gcm.NonceSize())
	if _, err = io.ReadFull(rand.Reader, nonce); err != nil {
		return "", err
	}

	ciphertext := gcm.Seal(nonce, nonce, plaintext, nil)
	return base64.URLEncoding.EncodeToString(ciphertext), nil
}

// Decrypt 解密字符串(GCM模式)
func Decrypt(cryptoText string) (string, error) {
	ciphertext, err := base64.URLEncoding.DecodeString(cryptoText)
	if err != nil {
		return "", err
	}

	block, err := aes.NewCipher(encryptionKey)
	if err != nil {
		return "", err
	}

	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return "", err
	}

	nonceSize := gcm.NonceSize()
	if len(ciphertext) < nonceSize {
		return "", errors.New("ciphertext too short")
	}

	nonce, ciphertext := ciphertext[:nonceSize], ciphertext[nonceSize:]
	plaintext, err := gcm.Open(nil, nonce, ciphertext, nil)
	if err != nil {
		return "", err
	}

	return string(plaintext), nil
}
