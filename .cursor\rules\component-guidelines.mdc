---
description: 
globs: 
alwaysApply: true
---
# React组件开发指南

## 组件结构
1. 每个组件应该放在 `frontend/src/components/` 目录下
2. 组件文件使用 `.tsx` 扩展名
3. 组件应该使用函数式组件和Hooks

## 组件命名规范
1. 组件文件名使用PascalCase（如：`RechargeDialog.tsx`）
2. 组件名称应该具有描述性，表明其功能
3. 对话框组件以`Dialog`结尾（如：`AboutDialog`、`ClearDataDialog`）

## 组件开发规范
1. 使用TypeScript类型定义
```typescript
interface ComponentProps {
  isOpen: boolean;
  onClose: () => void;
}
```

2. 使用Ant Design组件
```typescript
import { Button, Modal, Form } from 'antd';
```

3. 状态管理
```typescript
const [state, setState] = useState<StateType>(initialState);
```

4. 事件处理
```typescript
const handleSubmit = async () => {
  try {
    // 处理逻辑
  } catch (error) {
    // 错误处理
  }
};
```

## 对话框组件规范
1. 使用Ant Design的Dialog组件
2. 包含以下基本结构：
   - 标题
   - 关闭按钮
   - 内容区域
   - 操作按钮

## 样式规范
1. 遵循移动优先的响应式设计
2. 使用Ant Design的主题系统

## 组件通信
1. 使用Props进行父子组件通信
2. 使用Context进行全局状态管理
3. 使用Wails运行时进行前后端通信
```typescript
window.go.main.SomeMethod();
```
