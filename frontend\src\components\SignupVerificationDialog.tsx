import React, { useState } from "react";
import { Modal, Form, Input, Button, message, Steps, Result } from "antd";
import { MailOutlined, KeyOutlined } from "@ant-design/icons";
import { useAuth } from "../contexts/AuthContext";
import { useLoading } from "../contexts/LoadingContext";
import { LogError, LogInfo } from "../../wailsjs/runtime/runtime";

interface SignupVerificationDialogProps {
  isOpen: boolean;
  onClose: () => void;
  email: string; // 注册时使用的邮箱
  onVerificationSuccess?: () => void; // 验证成功后的回调函数
}

const { Step } = Steps;

const SignupVerificationDialog: React.FC<SignupVerificationDialogProps> = ({ isOpen, onClose, email, onVerificationSuccess }) => {
  const { verifySignupToken, login } = useAuth();
  const { isLoading, setLoading } = useLoading();
  const [tokenForm] = Form.useForm();
  const [currentStep, setCurrentStep] = useState<number>(0);
  const [verificationToken, setVerificationToken] = useState<string>("");
  const [tokenVerified, setTokenVerified] = useState<boolean>(false);
  const [verificationSuccess, setVerificationSuccess] = useState<boolean>(false);
  const [verifyingToken, setVerifyingToken] = useState<boolean>(false);

  // 处理关闭对话框
  const handleClose = () => {
    // 重置表单和状态
    tokenForm.resetFields();
    setCurrentStep(0);
    setVerificationToken("");
    setTokenVerified(false);
    setVerificationSuccess(false);
    setVerifyingToken(false);
    onClose();
  };

  // 验证注册令牌
  const handleVerifyToken = async (values: { token: string }) => {
    try {
      setVerifyingToken(true);
      const response = await verifySignupToken(email, values.token);

      if (response.success) {
        setVerificationToken(values.token);
        setTokenVerified(true);
        setVerificationSuccess(true);
        setCurrentStep(1);
        message.success("注册验证成功，您现在可以登录了");
        LogInfo(`注册验证令牌验证成功: ${response.message}`);

        // 调用验证成功的回调函数
        if (onVerificationSuccess) {
          onVerificationSuccess();
        }

        // 如果验证成功，自动登录用户
        if (response.access_token && response.refresh_token) {
          try {
            // 这里我们不需要密码，因为验证令牌已经提供了访问令牌
            // 但是我们的登录API需要密码，所以这里需要修改
            // 暂时不自动登录，让用户手动登录
          } catch (loginErr) {
            LogError(`自动登录失败: ${loginErr}`);
            // 登录失败不影响验证成功的结果
          }
        }
      } else {
        message.error("验证令牌失败，请检查后重试");
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : String(err);
      LogError(`验证注册令牌失败: ${errorMessage}`);

      // 提供更友好的错误信息
      let userFriendlyMessage = "验证令牌失败";

      if (errorMessage.includes("400")) {
        userFriendlyMessage = "令牌无效，请检查后重试";
      } else if (errorMessage.includes("401")) {
        userFriendlyMessage = "令牌已过期，请重新注册";
      } else if (errorMessage.includes("network") || errorMessage.includes("timeout")) {
        userFriendlyMessage = "网络连接异常，请检查网络后重试";
      }

      message.error(userFriendlyMessage);
    } finally {
      setVerifyingToken(false);
    }
  };

  // 渲染步骤内容
  const renderStepContent = () => {
    switch (currentStep) {
      case 0:
        return (
          <Form
            form={tokenForm}
            name="verifySignupToken"
            onFinish={handleVerifyToken}
            layout="vertical"
            size="large"
          >
            <p>我们已向 {email} 发送了一封包含验证令牌的邮件。</p>
            <p>请从邮件中获取验证令牌，并在下方输入以完成注册。</p>

            <Form.Item
              name="token"
              rules={[{ required: true, message: "请输入验证令牌!" }]}
            >
              <Input
                prefix={<KeyOutlined style={{ color: "#1890ff" }} />}
                placeholder="请输入验证令牌"
              />
            </Form.Item>

            <Form.Item>
              <Button
                type="primary"
                htmlType="submit"
                loading={verifyingToken}
                style={{ width: "100%" }}
              >
                验证令牌
              </Button>
            </Form.Item>
          </Form>
        );
      case 1:
        return (
          <Result
            status="success"
            title="注册验证成功!"
            subTitle="您的账号已激活，现在可以登录了"
            extra={[
              <Button type="primary" key="login" onClick={handleClose}>
                返回登录
              </Button>,
            ]}
          />
        );
      default:
        return null;
    }
  };

  return (
    <Modal
      title="注册验证"
      open={isOpen}
      onCancel={handleClose}
      footer={null}
      maskClosable={false}
      width={500}
    >
      <Steps current={currentStep} style={{ marginBottom: 20 }}>
        <Step title="验证令牌" />
        <Step title="完成" />
      </Steps>
      {renderStepContent()}
    </Modal>
  );
};

export default SignupVerificationDialog;
