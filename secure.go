package main

import (
  "crypto/rand"
  "encoding/binary"
  "sync"
)

type SecureToken struct {
  data  [1024]byte // 固定长度防长度泄露
  valid bool
}

var secureMutex sync.Mutex

func (s *SecureToken) Set(token string) {
  secureMutex.Lock()
  defer secureMutex.Unlock()

  // 1. 先填充随机数据
  rand.Read(s.data[:])

  // 2. 写入真实Token（带长度前缀）
  binary.LittleEndian.PutUint16(s.data[:2], uint16(len(token)))
  copy(s.data[2:], token)

  // 3. 混淆剩余空间
  for i := 2 + len(token); i < len(s.data); i++ {
    s.data[i] = ^s.data[i]
  }

  s.valid = true
}

func (s *SecureToken) Get() (string, bool) {
  secureMutex.Lock()
  defer secureMutex.Unlock()

  if !s.valid {
    return "", false
  }

  length := binary.LittleEndian.Uint16(s.data[:2])
  if int(length) > len(s.data)-2 {
    return "", false
  }

  return string(s.data[2 : 2+length]), true
}

func (s *SecureToken) Clear() {
  secureMutex.Lock()
  defer secureMutex.Unlock()
  // 1. 清空数据
  for i := range s.data {
    s.data[i] = 0
  }
}
