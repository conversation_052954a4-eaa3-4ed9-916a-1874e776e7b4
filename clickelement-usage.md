# ClickElement Method Usage Example

The new `ClickElement` method allows you to click any element on a webpage specified by a selector. Here's how to use it in your frontend code:

```typescript
import { ClickElement } from "../../wailsjs/go/main/App";

// Example: Click a button
async function clickSubmitButton() {
  try {
    await ClickElement("button[type='submit']");
    console.log("Submit button clicked successfully");
  } catch (error) {
    console.error("Failed to click submit button:", error);
  }
}

// Example: Click a link
async function clickLink() {
  try {
    await ClickElement("a.next-page");
    console.log("Link clicked successfully");
  } catch (error) {
    console.error("Failed to click link:", error);
  }
}

// Example: Click a tab
async function clickTab() {
  try {
    await ClickElement("div#tab-3");
    console.log("Tab clicked successfully");
  } catch (error) {
    console.error("Failed to click tab:", error);
  }
}
```

## Selector Examples

You can use various CSS selectors to target elements:

1. By ID: `#elementId`
2. By Class: `.className`
3. By Tag: `button`, `a`, `div`, etc.
4. By Attribute: `[type='submit']`
5. By Text Content: `button:has-text('Submit')`
6. By Combination: `div.form-group button[type='submit']`

## Error Handling

The method will return an error if:
- The page is not initialized
- The element is not found
- The element is not visible
- The element cannot be clicked

Always wrap your calls in try/catch blocks to handle these potential errors.

## Comparison with ClickButton

While the existing `ClickButton` method is specifically designed for buttons, the new `ClickElement` method is more general and can be used to click any element on the page, including:

- Buttons
- Links
- Tabs
- Images
- Checkboxes
- Radio buttons
- Any other clickable element

This makes `ClickElement` more versatile for general interaction with web pages.
