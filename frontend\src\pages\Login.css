.login-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  background: url('../assets/images/login-bg.svg') no-repeat center center;
  background-size: cover;
  position: relative;
  overflow: hidden;
}

/* 背景动画元素 */
.login-container::before {
  content: "";
  position: absolute;
  width: 300%;
  height: 300%;
  top: -100%;
  left: -100%;
  background: linear-gradient(
    45deg,
    rgba(255, 255, 255, 0.15) 25%,
    transparent 25%,
    transparent 50%,
    rgba(255, 255, 255, 0.15) 50%,
    rgba(255, 255, 255, 0.15) 75%,
    transparent 75%
  );
  background-size: 100px 100px;
  transform: rotate(45deg);
  z-index: 0;
  animation: login-bg-animation 60s linear infinite;
}

@keyframes login-bg-animation {
  0% {
    transform: rotate(45deg) translate(0, 0);
  }
  100% {
    transform: rotate(45deg) translate(-100px, -100px);
  }
}

.login-card {
  width: 420px;
  border-radius: 16px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  background-color: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  position: relative;
  z-index: 1;
  overflow: hidden;
  padding: 10px;
  border: 1px solid rgba(255, 255, 255, 0.5);
}

.login-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 5px;
  background: linear-gradient(90deg, #1890ff, #52c41a);
}

.login-header {
  text-align: center;
  margin-bottom: 20px;
  padding-top: 10px;
}

.login-header h2 {
  color: #1890ff;
  margin-bottom: 5px;
  font-weight: 600;
}

.login-header h4 {
  color: #666;
  font-weight: 400;
  margin-top: 0;
}

.login-form {
  padding: 0 20px;
}

.login-input {
  height: 45px;
  border-radius: 8px;
  margin-bottom: 5px;
}

.login-button {
  width: 100%;
  height: 45px;
  font-size: 16px;
  border-radius: 8px;
  background: linear-gradient(90deg, #1890ff, #52c41a);
  border: none;
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
  transition: all 0.3s ease;
}

.login-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 15px rgba(24, 144, 255, 0.4);
  background: linear-gradient(90deg, #40a9ff, #73d13d);
}

.login-checkbox {
  margin-top: 5px;
  margin-bottom: 15px;
}

.loading-placeholder {
  text-align: center;
  padding: 40px 0;
  color: #1890ff;
  font-size: 16px;
}

.login-tabs {
  width: 100%;
  margin-bottom: 10px;
}

.login-tabs .ant-tabs-nav {
  margin-bottom: 15px;
}

.login-tabs .ant-tabs-tab {
  padding: 8px 0;
  font-size: 16px;
  transition: all 0.3s;
}

.login-tabs .ant-tabs-tab:hover {
  color: #1890ff;
}

.login-tabs .ant-tabs-tab.ant-tabs-tab-active .ant-tabs-tab-btn {
  color: #1890ff;
  font-weight: 500;
}

.login-tabs .ant-tabs-ink-bar {
  background-color: #1890ff;
  height: 3px;
}
