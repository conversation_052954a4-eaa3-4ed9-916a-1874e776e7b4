package main

import (
	"bytes"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"image"
	"image/jpeg"
	"image/png"
	"math"
	"strings"
	"time"

	"github.com/playwright-community/playwright-go"
	"golang.org/x/image/draw"
)

// resizeImage 减小图片大小
// imageBytes: 原始图片字节数据
// maxWidth: 最大宽度，如果为0则保持原始宽度
// 返回: (压缩后的图片字节数据, 错误, 图片格式)
func resizeImage(imageBytes []byte, maxWidth int) ([]byte, string, error) {
	// 解码图片
	img, format, err := image.Decode(bytes.NewReader(imageBytes))
	if err != nil {
		return nil, "", fmt.Errorf("failed to decode image: %v", err)
	}

	// 获取原始尺寸
	bounds := img.Bounds()
	origWidth := bounds.Dx()
	origHeight := bounds.Dy()

	// 如果maxWidth为0或大于原始宽度，保持原始尺寸
	if maxWidth <= 0 || maxWidth >= origWidth {
		maxWidth = origWidth
	}

	// 计算新的高度，保持宽高比
	newHeight := int(math.Round(float64(origHeight) * float64(maxWidth) / float64(origWidth)))

	// 如果尺寸没有变化且质量为默认值，直接返回原图
	if maxWidth == origWidth {
		return imageBytes, format, nil
	}

	// 创建目标尺寸的图像
	dst := image.NewRGBA(image.Rect(0, 0, maxWidth, newHeight))

	// 使用CatmullRom算法进行高质量缩放
	draw.CatmullRom.Scale(dst, dst.Bounds(), img, bounds, draw.Over, nil)

	// 创建一个新的buffer
	buf := new(bytes.Buffer)

	// 根据原始图片格式进行编码
	format = strings.ToLower(format)
	if format == "jpeg" || format == "jpg" {
		// 使用JPEG编码，质量设为85（平衡质量和大小）
		err = jpeg.Encode(buf, dst, &jpeg.Options{Quality: 85})
	} else {
		// 默认使用PNG编码
		err = png.Encode(buf, dst)
		format = "png"
	}

	if err != nil {
		return nil, "", fmt.Errorf("failed to encode image: %v", err)
	}

	return buf.Bytes(), format, nil
}

// ScreenshotElement 抓取指定选择器的元素截图并转换为base64编码字符串
// selector: CSS选择器或文本选择器
// 返回: (base64编码的图片, 错误)
func (a *App) ScreenshotElement(selector string, maxWidth int) (string, error) {
	if a.page == nil {
		return "", fmt.Errorf("page not initialized")
	}
	// 查找元素
	locator := a.page.Locator(selector).First()
	if locator == nil {
		return "", fmt.Errorf("element not found: %s", selector)
	}
	// 检查元素是否可见
	visible, err := locator.IsVisible()
	if err != nil || !visible {
		return "", fmt.Errorf("element not visible: %s, error: %v", selector, err)
	}
	// 抓取元素截图，默认为PNG格式
	screenshotBytes, err := locator.Screenshot()
	if err != nil {
		return "", fmt.Errorf("failed to take screenshot: %v", err)
	}
	// 压缩图片
	var processedBytes []byte
	var format string
	compressedBytes, format, err := resizeImage(screenshotBytes, maxWidth)
	if err != nil {
		fmt.Printf("压缩图片失败: %v, 使用原图\n", err)
		processedBytes = screenshotBytes
		format = "png" // 默认为PNG格式
	} else {
		processedBytes = compressedBytes
	}
	// 将图像转换为base64编码
	imageBase64 := base64.StdEncoding.EncodeToString(processedBytes)
	// 添加data URL前缀，使用正确的MIME类型
	var dataURL string
	if format == "jpeg" || format == "jpg" {
		dataURL = "data:image/jpeg;base64," + imageBase64
	} else {
		dataURL = "data:image/png;base64," + imageBase64
	}

	return dataURL, nil
}

// ClickElement 点击指定选择器的元素
// selector: CSS选择器或文本选择器
// 返回: 操作过程中遇到的错误
func (a *App) ClickElement(selector string) error {
	if a.page == nil {
		return fmt.Errorf("page not initialized")
	}

	// 查找元素
	locator := a.page.Locator(selector).First()
	if locator == nil {
		return fmt.Errorf("element not found: %s", selector)
	}

	// 检查元素是否可见
	visible, err := locator.IsVisible()
	if err != nil || !visible {
		return fmt.Errorf("element not visible: %s, error: %v", selector, err)
	}

	// 点击元素
	if err := locator.Click(); err != nil {
		return fmt.Errorf("failed to click element: %v", err)
	}

	if AppLogger != nil {
		AppLogger.Debug("已点击元素: %s", selector)
	}

	return nil
}

// FillElement 填充指定选择器的元素
// selector: CSS选择器或文本选择器
// value: 要填充的值
// 返回: 操作过程中遇到的错误
func (a *App) FillElement(selector string, value string) error {
	if a.page == nil {
		return fmt.Errorf("page not initialized")
	}

	// 查找元素
	locator := a.page.Locator(selector).First()
	if locator == nil {
		return fmt.Errorf("element not found: %s", selector)
	}

	// 检查元素是否可见
	visible, err := locator.IsVisible()
	if err != nil || !visible {
		return fmt.Errorf("element not visible: %s, error: %v", selector, err)
	}

	// 填充元素
	if err := locator.PressSequentially(value); err != nil {
		return fmt.Errorf("failed to fill element: %v", err)
	}

	if AppLogger != nil {
		AppLogger.Debug("已填充元素 %s 的值为: %s", selector, value)
	}

	return nil
}

// ScreenshotArea 截取页面指定区域的截图并转换为base64编码字符串
// x, y: 截图区域的左上角坐标
// width, height: 截图区域的宽度和高度
// maxWidth: 最大宽度，如果为0则保持原始宽度
// 返回: (base64编码的图片, 错误)
func (a *App) ScreenshotArea(x, y, width, height, maxWidth int) (string, error) {
	if a.page == nil {
		return "", fmt.Errorf("page not initialized")
	}
	// 确保参数有效
	if width <= 0 || height <= 0 {
		return "", fmt.Errorf("invalid dimensions: width and height must be positive")
	}

	if x < 0 || y < 0 {
		return "", fmt.Errorf("invalid coordinates: x and y must be non-negative")
	}
	// 创建截图选项
	options := playwright.PageScreenshotOptions{
		Clip: &playwright.Rect{
			X:      float64(x),
			Y:      float64(y),
			Width:  float64(width),
			Height: float64(height),
		},
	}
	// 使用Playwright的Screenshot方法截取指定区域
	screenshotBytes, err := a.page.Screenshot(options)
	if err != nil {
		return "", fmt.Errorf("failed to take screenshot: %v", err)
	}

	if AppLogger != nil {
		AppLogger.Debug("已截取区域截图: x=%d, y=%d, width=%d, height=%d", x, y, width, height)
	}
	// 压缩图片
	var processedBytes []byte
	var format string
	compressedBytes, format, err := resizeImage(screenshotBytes, maxWidth)
	if err != nil {
		if AppLogger != nil {
			AppLogger.Warning("压缩图片失败: %v, 使用原图", err)
		}
		processedBytes = screenshotBytes
		format = "png" // 默认为PNG格式
	} else {
		processedBytes = compressedBytes
	}
	// 将图像转换为base64编码
	imageBase64 := base64.StdEncoding.EncodeToString(processedBytes)
	// 添加data URL前缀，使用正确的MIME类型
	var dataURL string
	if format == "jpeg" || format == "jpg" {
		dataURL = "data:image/jpeg;base64," + imageBase64
	} else {
		dataURL = "data:image/png;base64," + imageBase64
	}

	return dataURL, nil
}

// CaptureFullPage 截取整个页面的截图并转换为base64编码字符串
// 返回: (base64编码的图片, 错误)
func (a *App) CaptureFullPage() (string, error) {
	if a.page == nil {
		return "", fmt.Errorf("page not initialized")
	}

	// 使用Playwright的Screenshot方法截取整个页面
	options := playwright.PageScreenshotOptions{
		FullPage: playwright.Bool(true),
	}
	screenshotBytes, err := a.page.Screenshot(options)
	if err != nil {
		return "", fmt.Errorf("failed to take full page screenshot: %v", err)
	}

	if AppLogger != nil {
		AppLogger.Debug("已截取整个页面截图")
	}

	// 压缩图片，最大宽度设为1200，保持较好的清晰度
	var processedBytes []byte
	var format string
	compressedBytes, format, err := resizeImage(screenshotBytes, 1200)
	if err != nil {
		if AppLogger != nil {
			AppLogger.Warning("压缩图片失败: %v, 使用原图", err)
		}
		processedBytes = screenshotBytes
		format = "png" // 默认为PNG格式
	} else {
		processedBytes = compressedBytes
	}

	// 将图像转换为base64编码
	imageBase64 := base64.StdEncoding.EncodeToString(processedBytes)

	// 添加data URL前缀，使用正确的MIME类型
	var dataURL string
	if format == "jpeg" || format == "jpg" {
		dataURL = "data:image/jpeg;base64," + imageBase64
	} else {
		dataURL = "data:image/png;base64," + imageBase64
	}

	return dataURL, nil
}

// 元素操作信息
type ElementOperation struct {
	Selector    string `json:"selector"`
	Description string `json:"description"`
	Type        string `json:"type"` // "click" 或 "input"
	Value       string `json:"value,omitempty"`
}

// ExecuteElementOperations 执行一系列元素操作
// operations: 要执行的操作列表
// 返回: 操作过程中遇到的错误
func (a *App) ExecuteElementOperations(operationsJson string) error {
	if a.page == nil {
		return fmt.Errorf("page not initialized")
	}

	// 解析操作列表
	var operations []ElementOperation
	if err := json.Unmarshal([]byte(operationsJson), &operations); err != nil {
		return fmt.Errorf("failed to parse operations: %v", err)
	}

	if len(operations) == 0 {
		return fmt.Errorf("no operations to execute")
	}

	if AppLogger != nil {
		AppLogger.Info("开始执行元素操作，共 %d 个操作", len(operations))
	}

	// 执行每个操作
	for i, op := range operations {
		if AppLogger != nil {
			AppLogger.Debug("执行操作 %d/%d: %s (%s)", i+1, len(operations), op.Description, op.Type)
		}

		// 根据操作类型执行不同的操作
		switch op.Type {
		case "click":
			if err := a.ClickElement(op.Selector); err != nil {
				return fmt.Errorf("failed to click element '%s': %v", op.Description, err)
			}
			// 点击后等待一小段时间，让页面响应
			time.Sleep(500 * time.Millisecond)
		case "input":
			if err := a.FillElement(op.Selector, op.Value); err != nil {
				return fmt.Errorf("failed to fill element '%s': %v", op.Description, err)
			}
			// 输入后等待一小段时间
			time.Sleep(300 * time.Millisecond)
		default:
			return fmt.Errorf("unknown operation type: %s", op.Type)
		}
	}

	if AppLogger != nil {
		AppLogger.Info("元素操作执行完成")
	}

	return nil
}

// SelectElement 让用户选择网页元素并返回选择器
// 使用Chrome DevTools Protocol (CDP) 实现，替代JavaScript注入方式
// 返回: (CSS选择器, 错误)
func (a *App) SelectElement() (string, error) {
	if a.page == nil {
		return "", fmt.Errorf("page not initialized")
	}

	if AppLogger != nil {
		AppLogger.Debug("开始选择网页元素 (使用CDP)")
	}

	// 获取浏览器上下文
	context := browser
	if context == nil {
		return "", fmt.Errorf("browser context not available")
	}

	// 创建CDP会话
	cdpSession, err := context.NewCDPSession(a.page)
	if err != nil {
		return "", fmt.Errorf("创建CDP会话失败: %v", err)
	}

	// 确保在函数结束时清理资源
	defer func() {
		// 禁用审查模式
		cdpSession.Send("Overlay.setInspectMode", map[string]any{
			"mode": "none",
		})
		// 分离CDP会话
		cdpSession.Detach()
	}()

	// 启用必要的CDP域，添加超时控制
	domains := []string{"DOM", "Overlay", "Runtime"}
	for _, domain := range domains {
		if _, err := cdpSession.Send(domain+".enable", nil); err != nil {
			return "", fmt.Errorf("启用%s域失败: %v", domain, err)
		}
	}

	// 创建通道用于接收选择结果
	resultChan := make(chan string, 1)
	errorChan := make(chan error, 1)
	cancelChan := make(chan bool, 1)

	// 监听inspectNodeRequested事件
	cdpSession.On("Overlay.inspectNodeRequested", func(params map[string]any) {
		if AppLogger != nil {
			AppLogger.Debug("收到元素选择事件")
		}

		// 使用defer确保审查模式被禁用
		defer func() {
			if _, err := cdpSession.Send("Overlay.setInspectMode", map[string]any{
				"mode": "none",
			}); err != nil && AppLogger != nil {
				AppLogger.Warning("禁用审查模式失败: %v", err)
			}
		}()

		// 获取backendNodeId
		backendNodeId, ok := params["backendNodeId"]
		if !ok {
			if AppLogger != nil {
				AppLogger.Error("未找到backendNodeId参数")
			}
			select {
			case errorChan <- fmt.Errorf("未找到backendNodeId"):
			default:
				// 防止阻塞
			}
			return
		}

		// 生成选择器
		selector, err := a.generateSelectorFromNodeId(cdpSession, backendNodeId)
		if err != nil {
			if AppLogger != nil {
				AppLogger.Error("生成选择器失败: %v", err)
			}
			select {
			case errorChan <- fmt.Errorf("生成选择器失败: %v", err):
			default:
				// 防止阻塞
			}
			return
		}

		if AppLogger != nil {
			AppLogger.Debug("成功生成选择器: %s", selector)
		}

		select {
		case resultChan <- selector:
		default:
			// 防止阻塞
		}
	})

	// 监听键盘事件以支持ESC取消
	cdpSession.On("Runtime.consoleAPICalled", func(params map[string]any) {
		// 这里可以处理控制台消息，但我们主要依赖页面级别的键盘事件
	})

	// 启用元素审查模式
	inspectParams := map[string]any{
		"mode": "searchForNode",
		"highlightConfig": map[string]any{
			"showInfo":           true,
			"showRulers":         false,
			"showExtensionLines": false,
			"contentColor": map[string]any{
				"r": 111,
				"g": 168,
				"b": 220,
				"a": 0.66,
			},
			"paddingColor": map[string]any{
				"r": 147,
				"g": 196,
				"b": 125,
				"a": 0.55,
			},
			"borderColor": map[string]any{
				"r": 255,
				"g": 229,
				"b": 153,
				"a": 0.66,
			},
			"marginColor": map[string]any{
				"r": 246,
				"g": 178,
				"b": 107,
				"a": 0.66,
			},
		},
	}

	if _, err := cdpSession.Send("Overlay.setInspectMode", inspectParams); err != nil {
		return "", fmt.Errorf("启用审查模式失败: %v", err)
	}

	// 注入JavaScript来处理ESC键取消
	escapeScript := `
		(function() {
			const handleEscape = function(e) {
				if (e.key === 'Escape') {
					console.log('ELEMENT_SELECTION_CANCELLED');
					document.removeEventListener('keydown', handleEscape);
				}
			};
			document.addEventListener('keydown', handleEscape);

			// 添加视觉提示
			const hint = document.createElement('div');
			hint.id = 'element-selection-hint';
			hint.style.cssText = 'position: fixed; top: 10px; left: 50%; transform: translateX(-50%); background: rgba(0, 0, 0, 0.8); color: white; padding: 8px 16px; border-radius: 4px; font-family: Arial, sans-serif; font-size: 14px; z-index: 999999; pointer-events: none;';
			hint.textContent = '请选择要操作的元素，按ESC键取消';
			document.body.appendChild(hint);

			// 5秒后自动移除提示
			setTimeout(() => {
				const hintEl = document.getElementById('element-selection-hint');
				if (hintEl) {
					hintEl.remove();
				}
			}, 5000);
		})();
	`
	if _, err := a.page.Evaluate(escapeScript); err != nil {
		if AppLogger != nil {
			AppLogger.Warning("注入ESC处理脚本失败: %v", err)
		}
	}

	// 监听控制台消息以处理取消操作
	consoleHandler := func(msg any) {
		if consoleMsg, ok := msg.(playwright.ConsoleMessage); ok {
			if consoleMsg.Text() == "ELEMENT_SELECTION_CANCELLED" {
				if AppLogger != nil {
					AppLogger.Debug("检测到用户按下ESC键，取消元素选择")
				}
				select {
				case cancelChan <- true:
				default:
					// 防止阻塞
				}
			}
		}
	}

	a.page.On("console", consoleHandler)
	defer func() {
		// 清理提示元素
		a.page.Evaluate(`
			const hint = document.getElementById('element-selection-hint');
			if (hint) hint.remove();
		`)
	}()

	if AppLogger != nil {
		AppLogger.Debug("已启用元素审查模式，请在浏览器中选择元素")
	}

	// 等待结果、错误或取消
	select {
	case selector := <-resultChan:
		if AppLogger != nil && selector != "" {
			AppLogger.Debug("已选择元素，选择器: %s", selector)
		}
		return selector, nil
	case err := <-errorChan:
		// 确保禁用审查模式
		cdpSession.Send("Overlay.setInspectMode", map[string]any{
			"mode": "none",
		})
		return "", err
	case <-cancelChan:
		if AppLogger != nil {
			AppLogger.Debug("用户取消了元素选择")
		}
		return "", nil
	case <-time.After(60 * time.Second): // 设置超时时间
		// 确保禁用审查模式
		cdpSession.Send("Overlay.setInspectMode", map[string]any{
			"mode": "none",
		})
		return "", fmt.Errorf("选择元素超时")
	}
}

// generateSelectorFromNodeId 根据CDP节点ID生成CSS选择器
func (a *App) generateSelectorFromNodeId(cdpSession playwright.CDPSession, backendNodeId any) (string, error) {
	// 将backendNodeId转换为整数，支持多种数字类型
	var nodeId int
	switch v := backendNodeId.(type) {
	case float64:
		nodeId = int(v)
	case int:
		nodeId = v
	case int64:
		nodeId = int(v)
	default:
		return "", fmt.Errorf("无效的backendNodeId类型: %T", backendNodeId)
	}

	if AppLogger != nil {
		AppLogger.Debug("正在为节点ID %d 生成选择器", nodeId)
	}

	// 获取节点的详细信息
	nodeInfo, err := cdpSession.Send("DOM.describeNode", map[string]any{
		"backendNodeId": nodeId,
	})
	if err != nil {
		return "", fmt.Errorf("获取节点信息失败: %v", err)
	}

	nodeInfoMap, ok := nodeInfo.(map[string]any)
	if !ok {
		return "", fmt.Errorf("节点信息格式错误")
	}

	node, ok := nodeInfoMap["node"].(map[string]any)
	if !ok {
		return "", fmt.Errorf("节点数据格式错误")
	}

	// 尝试生成最优选择器
	selector := a.generateOptimalSelector(cdpSession, node)
	if selector != "" {
		if AppLogger != nil {
			AppLogger.Debug("生成最优选择器: %s", selector)
		}
		// 验证选择器的唯一性
		if a.validateSelector(selector) {
			return selector, nil
		} else if AppLogger != nil {
			AppLogger.Debug("选择器 %s 不唯一，尝试生成完整路径", selector)
		}
	}

	// 如果无法生成最优选择器，使用完整路径
	fullPathSelector, err := a.generateFullPathSelector(cdpSession, node)
	if err != nil {
		// 如果完整路径也失败，返回一个基本的选择器
		if selector != "" {
			if AppLogger != nil {
				AppLogger.Warning("完整路径生成失败，使用非唯一选择器: %s", selector)
			}
			return selector, nil
		}
		return "", fmt.Errorf("无法生成任何有效选择器: %v", err)
	}

	return fullPathSelector, nil
}

// generateOptimalSelector 生成最优的CSS选择器
func (a *App) generateOptimalSelector(cdpSession playwright.CDPSession, node map[string]any) string {
	// 获取节点的基本信息
	nodeName, _ := node["nodeName"].(string)
	attributes, _ := node["attributes"].([]any)

	// 将属性数组转换为map
	attrMap := make(map[string]string)
	for i := 0; i < len(attributes)-1; i += 2 {
		if key, ok := attributes[i].(string); ok {
			if value, ok := attributes[i+1].(string); ok {
				attrMap[key] = value
			}
		}
	}

	// 1. 尝试使用ID选择器
	if id, exists := attrMap["id"]; exists && id != "" {
		return "#" + id
	}

	// 2. 尝试使用name属性
	if name, exists := attrMap["name"]; exists && name != "" {
		return fmt.Sprintf("[name=\"%s\"]", name)
	}

	// 3. 尝试使用data-testid属性
	if testId, exists := attrMap["data-testid"]; exists && testId != "" {
		return fmt.Sprintf("[data-testid=\"%s\"]", testId)
	}

	// 4. 尝试使用类名选择器
	if class, exists := attrMap["class"]; exists && class != "" {
		classes := strings.Fields(class)
		if len(classes) > 0 {
			// 尝试单个类名
			for _, cls := range classes {
				if cls != "" {
					selector := "." + cls
					if a.validateSelector(selector) {
						return selector
					}
				}
			}
			// 尝试组合类名
			return "." + strings.Join(classes, ".")
		}
	}

	// 5. 尝试使用标签名和属性组合
	if nodeName != "" {
		tagName := strings.ToLower(nodeName)

		// 对于input元素，尝试使用type属性
		if tagName == "input" {
			if inputType, exists := attrMap["type"]; exists && inputType != "" {
				return fmt.Sprintf("input[type=\"%s\"]", inputType)
			}
		}

		// 尝试使用标签名和类名组合
		if class, exists := attrMap["class"]; exists && class != "" {
			classes := strings.Fields(class)
			if len(classes) > 0 {
				return tagName + "." + classes[0]
			}
		}

		return tagName
	}

	return ""
}

// generateFullPathSelector 生成完整路径的CSS选择器
func (a *App) generateFullPathSelector(cdpSession playwright.CDPSession, node map[string]any) (string, error) {
	var path []string
	currentNode := node

	for currentNode != nil {
		selector := a.generateNodeSelector(currentNode)
		if selector != "" {
			path = append([]string{selector}, path...)
		}

		// 获取父节点
		parentId, exists := currentNode["parentId"]
		if !exists {
			break
		}

		parentNodeInfo, err := cdpSession.Send("DOM.describeNode", map[string]any{
			"nodeId": parentId,
		})
		if err != nil {
			break
		}

		parentNodeInfoMap, ok := parentNodeInfo.(map[string]any)
		if !ok {
			break
		}

		parentNode, ok := parentNodeInfoMap["node"].(map[string]any)
		if !ok {
			break
		}

		currentNode = parentNode
	}

	if len(path) > 0 {
		return strings.Join(path, " > "), nil
	}

	return "", fmt.Errorf("无法生成选择器路径")
}

// generateNodeSelector 为单个节点生成选择器
func (a *App) generateNodeSelector(node map[string]any) string {
	nodeName, _ := node["nodeName"].(string)
	if nodeName == "" {
		return ""
	}

	tagName := strings.ToLower(nodeName)
	attributes, _ := node["attributes"].([]any)

	// 将属性数组转换为map
	attrMap := make(map[string]string)
	for i := 0; i < len(attributes)-1; i += 2 {
		if key, ok := attributes[i].(string); ok {
			if value, ok := attributes[i+1].(string); ok {
				attrMap[key] = value
			}
		}
	}

	// 如果有ID，直接返回ID选择器
	if id, exists := attrMap["id"]; exists && id != "" {
		return "#" + id
	}

	selector := tagName

	// 添加类名
	if class, exists := attrMap["class"]; exists && class != "" {
		classes := strings.Fields(class)
		if len(classes) > 0 {
			selector += "." + strings.Join(classes, ".")
		}
	}

	// 对于input元素，添加type属性
	if tagName == "input" {
		if inputType, exists := attrMap["type"]; exists && inputType != "" {
			selector += fmt.Sprintf("[type=\"%s\"]", inputType)
		}
	}

	// 添加name属性
	if name, exists := attrMap["name"]; exists && name != "" {
		selector += fmt.Sprintf("[name=\"%s\"]", name)
	}

	return selector
}

// validateSelector 验证选择器的唯一性
func (a *App) validateSelector(selector string) bool {
	if a.page == nil {
		return false
	}

	// 使用JavaScript验证选择器
	script := fmt.Sprintf(`
		try {
			const elements = document.querySelectorAll('%s');
			return elements.length === 1;
		} catch (e) {
			return false;
		}
	`, selector)

	result, err := a.page.Evaluate(script)
	if err != nil {
		return false
	}

	isUnique, ok := result.(bool)
	return ok && isUnique
}
