# 充值管理 API 文档

> **重要说明**：
>
> - 充值申请表和充值统计表中的 `amount` 和 `total_amount` 字段表示**人民币金额**
> - 钱包表和交易表中的 `balance` 和 `amount` 字段表示**积分**
> - 换算关系：**1 元人民币 = 1000 积分**

## 用户充值相关 API

### 1. 提交充值申请

**请求**:

- 方法: `POST`
- 路径: `/api/v1/recharge/request`
- 需要认证: 是

**请求体**:

```json
{
  "amount": 100, // 充值金额（人民币，必填）
  "order_id": "2023xxx", // 支付宝订单号（必填）
  "payment_proof": "https://example.com/proof.jpg" // 支付凭证URL（可选）
}
```

**响应**:

```json
{
  "success": true,
  "recharge_id": 123,
  "request_info": {
    "id": 123,
    "user_id": "user-uuid",
    "rmb_amount": 100, // 人民币金额
    "order_id": "2023xxx",
    "status": "pending",
    "payment_method": "alipay",
    "payment_proof": "https://example.com/proof.jpg",
    "created_at": 1633506000,
    "updated_at": 1633506000
  },
  "message": "充值申请已提交，等待管理员审核"
}
```

### 2. 查询用户充值申请列表

**请求**:

- 方法: `GET`
- 路径: `/api/v1/recharge/list?limit=20&offset=0`
- 需要认证: 是

**参数**:

- `limit`: 每页数量，默认 20
- `offset`: 偏移量，默认 0

**响应**:

```json
{
  "success": true,
  "requests": [
    {
      "id": 123,
      "user_id": "user-uuid",
      "rmb_amount": 100, // 人民币金额
      "order_id": "2023xxx",
      "status": "pending",
      "payment_method": "alipay",
      "payment_proof": "https://example.com/proof.jpg",
      "admin_note": "",
      "processed_by": "",
      "processed_at": 0,
      "created_at": 1633506000,
      "updated_at": 1633506000
    }
  ],
  "count": 1
}
```

## 状态码说明

- 200: 请求成功
- 400: 请求参数错误
- 401: 未授权，需要登录
- 403: 权限不足，不是管理员
- 500: 服务器内部错误

## 充值流程说明

1. 用户提交充值申请，包含充值金额（人民币）和支付宝订单号
2. 管理员在后台审核充值申请，验证支付宝订单
3. 管理员批准充值后，系统自动将人民币金额转换为积分（1 元人民币=1000 积分）并充值到用户账户
4. 用户可在前端查看充值记录和当前积分余额
