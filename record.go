package main

import (
	"fmt"
	"time"

	"gorm.io/gorm"
)

// SaveGradingRecord 保存阅卷记录到数据库
// 参数:
// - db: 数据库连接
// - userEmail: 用户邮箱
// - answerImage: 答案原图(base64编码)
// - gradingCriteria: 评分标准
// - answerText: 学生答案文本
// - score: 得分
// - scoreDetails: 得分细节
// - apiCost: API调用花费(元)
// - promptTokens: prompt tokens用量
// - completionTokens: completion tokens用量
// 返回:
// - 错误信息
func SaveGradingRecord(
	db *gorm.DB,
	userEmail string,
	answerImage string,
	gradingCriteria string,
	answerText string,
	score float64,
	scoreDetails string,
) error {
	// 生成图片哈希值
	imageHash := GenerateImageHash(answerImage)

	// 查找或创建评分标准记录，获取评分标准ID
	criteriaID, err := FindOrCreateGradingCriteria(db, gradingCriteria)
	if err != nil {
		return fmt.Errorf("处理评分标准失败: %v", err)
	}

	// 检查是否已存在相同哈希值的记录
	var existingRecord GradingRecord
	result := db.Where("answer_image_hash = ?", imageHash).First(&existingRecord)

	// 如果已存在相同哈希值的记录，则更新该记录
	if result.Error == nil {
		if AppLogger != nil {
			AppLogger.Info("已存在相同答案图片的记录，更新记录 ID: %d", existingRecord.ID)
		}

		// 更新记录
		existingRecord.UserEmail = userEmail
		existingRecord.AnswerImage = answerImage
		existingRecord.GradingCriteriaID = criteriaID // 更新为评分标准ID
		existingRecord.AnswerText = answerText
		existingRecord.Score = score
		existingRecord.ScoreDetails = scoreDetails

		// 保存更新
		if err := db.Save(&existingRecord).Error; err != nil {
			return fmt.Errorf("更新阅卷记录失败: %v", err)
		}

		if AppLogger != nil {
			AppLogger.Info("成功更新阅卷记录，ID: %d", existingRecord.ID)
		}

		return nil
	}

	// 创建新记录
	record := GradingRecord{
		UserEmail:         userEmail,
		AnswerImage:       answerImage,
		AnswerImageHash:   imageHash,
		GradingCriteriaID: criteriaID, // 使用评分标准ID
		AnswerText:        answerText,
		Score:             score,
		ScoreDetails:      scoreDetails,
	}

	// 保存到数据库
	if err := db.Create(&record).Error; err != nil {
		return fmt.Errorf("保存阅卷记录失败: %v", err)
	}

	if AppLogger != nil {
		AppLogger.Info("成功保存阅卷记录，ID: %d", record.ID)
	}

	return nil
}

// FindGradingRecordByImageHash 根据图片哈希值查找阅卷记录
// 参数:
// - db: 数据库连接
// - imageHash: 图片哈希值
// 返回:
// - 阅卷记录
// - 是否找到
// - 错误信息
func FindGradingRecordByImageHash(db *gorm.DB, imageHash string) (*GradingRecord, bool, error) {
	var record GradingRecord
	result := db.Preload("GradingCriteria").Where("answer_image_hash = ?", imageHash).First(&record)

	if result.Error != nil {
		if result.Error == gorm.ErrRecordNotFound {
			return nil, false, nil
		}
		return nil, false, result.Error
	}

	return &record, true, nil
}

// FindGradingRecordByImage 根据图片内容查找阅卷记录
// 参数:
// - db: 数据库连接
// - image: 图片内容(base64编码)
// 返回:
// - 阅卷记录
// - 是否找到
// - 错误信息
func FindGradingRecordByImage(db *gorm.DB, image string) (*GradingRecord, bool, error) {
	// 生成图片哈希值
	imageHash := GenerateImageHash(image)

	// 使用哈希值查找记录
	return FindGradingRecordByImageHash(db, imageHash)
}

// GradingRecordListItem 表示阅卷记录列表项
type GradingRecordListItem struct {
	CreatedAt       time.Time `json:"created_at"`
	UserEmail       string    `json:"user_email"`
	AnswerImageHash string    `json:"answer_image_hash"`
	Score           float64   `json:"score"`
	AnswerImage     string    `json:"answer_image"`     // Base64编码的图片
	GradingCriteria string    `json:"grading_criteria"` // 评分标准内容（从关联表获取）
	AnswerText      string    `json:"answer_text"`      // 学生答案文本
	ScoreDetails    string    `json:"score_details"`    // 得分细节
}

// GradingRecordsResult 表示阅卷记录查询结果
type GradingRecordsResult struct {
	Records []GradingRecordListItem `json:"records"`
	Total   int64                   `json:"total"`
	Error   string                  `json:"error,omitempty"`
}

// DeleteGradingRecordsByImageHashes 根据图片哈希值列表删除阅卷记录
// 参数:
// - db: 数据库连接
// - imageHashes: 图片哈希值列表
// 返回:
// - 删除的记录数
// - 错误信息
func DeleteGradingRecordsByImageHashes(db *gorm.DB, imageHashes []string) (int64, error) {
	if len(imageHashes) == 0 {
		return 0, nil
	}

	// 执行删除操作
	result := db.Where("answer_image_hash IN ?", imageHashes).Delete(&GradingRecord{})
	if result.Error != nil {
		return 0, fmt.Errorf("删除阅卷记录失败: %v", result.Error)
	}

	if AppLogger != nil {
		AppLogger.Info("成功删除 %d 条阅卷记录", result.RowsAffected)
	}

	return result.RowsAffected, nil
}

// GetGradingRecords 获取阅卷记录列表
// 参数:
// - db: 数据库连接
// 返回:
// - 阅卷记录查询结果
func GetGradingRecords(db *gorm.DB) GradingRecordsResult {
	var records []GradingRecordListItem
	var total int64

	// 查询总记录数
	if err := db.Model(&GradingRecord{}).Count(&total).Error; err != nil {
		return GradingRecordsResult{
			Records: []GradingRecordListItem{},
			Total:   0,
			Error:   fmt.Sprintf("查询总记录数失败: %v", err),
		}
	}

	// 构建查询
	query := db.Model(&GradingRecord{}).
		Joins("LEFT JOIN grading_criteria ON grading_records.grading_criteria_id = grading_criteria.id").
		Select(
			"grading_records.created_at, grading_records.user_email, grading_records.answer_image_hash, " +
				"grading_records.score, grading_records.answer_image, grading_criteria.content as grading_criteria, " +
				"grading_records.answer_text, grading_records.score_details",
		).Order("grading_records.created_at DESC")

	// 执行查询
	if err := query.Find(&records).Error; err != nil {
		return GradingRecordsResult{
			Records: []GradingRecordListItem{},
			Total:   total,
			Error:   fmt.Sprintf("查询阅卷记录失败: %v", err),
		}
	}

	return GradingRecordsResult{
		Records: records,
		Total:   total,
		Error:   "",
	}
}

// 根据时间范围获取阅卷记录列表
func GetGradingRecordsByTimeRange(db *gorm.DB, startTime, endTime time.Time) []GradingRecordListItem {
	var records []GradingRecordListItem
	// 构建查询
	query := db.Model(&GradingRecord{}).
		Joins("LEFT JOIN grading_criteria ON grading_records.grading_criteria_id = grading_criteria.id").
		Select(
			"grading_records.created_at, grading_records.user_email, grading_records.answer_image_hash, "+
				"grading_records.score, grading_records.answer_image, grading_criteria.content as grading_criteria, "+
				"grading_records.answer_text, grading_records.score_details").
		Where("grading_records.created_at >= ? AND grading_records.created_at <= ?", startTime, endTime).
		Order("grading_records.created_at DESC")
	// 执行查询
	if err := query.Find(&records).Error; err != nil {
		return []GradingRecordListItem{}
	}
	return records
}

// 获取评分标准列表
type GradingCriteriaItem struct {
	ID      string `json:"id"`
	Content string `json:"content"`
}

// 获取所有评分标准列表
func GetGradingCriteriaList(db *gorm.DB) []GradingCriteriaItem {
	var criteriaList []GradingCriteriaItem
	// 构建查询
	query := db.Model(&GradingCriteria{}).
		Select("id, content").
		Order("created_at DESC")
	// 执行查询
	if err := query.Find(&criteriaList).Error; err != nil {
		return []GradingCriteriaItem{}
	}
	return criteriaList
}

// 根据时间范围和评分标准ID获取阅卷记录列表
func GetGradingRecordsByTimeRangeAndCriteria(db *gorm.DB, criteriaID string, startTime, endTime time.Time) []GradingRecordListItem {
	var records []GradingRecordListItem
	// 构建查询
	query := db.Model(&GradingRecord{}).
		Joins("LEFT JOIN grading_criteria ON grading_records.grading_criteria_id = grading_criteria.id").
		Select(
			"grading_records.created_at, grading_records.user_email, grading_records.answer_image_hash, "+
				"grading_records.score, grading_records.answer_image, grading_criteria.content as grading_criteria, "+
				"grading_records.answer_text, grading_records.score_details",
		).Where("grading_records.grading_criteria_id = ?", criteriaID).
		Where("grading_records.created_at >= ? AND grading_records.created_at <= ?", startTime, endTime).
		Order("grading_records.created_at DESC")
	// 执行查询
	if err := query.Find(&records).Error; err != nil {
		return []GradingRecordListItem{}
	}
	return records
}
