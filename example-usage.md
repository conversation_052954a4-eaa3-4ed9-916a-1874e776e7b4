# FillElement Method Usage Example

The new `FillElement` method allows you to fill any input element on a webpage with a specified value. Here's how to use it in your frontend code:

```typescript
import { FillElement } from "../../wailsjs/go/main/App";

// Example: Fill a username input field
async function fillUsername(username: string) {
  try {
    await FillElement("input#username", username);
    console.log("Username filled successfully");
  } catch (error) {
    console.error("Failed to fill username:", error);
  }
}

// Example: Fill a password input field
async function fillPassword(password: string) {
  try {
    await FillElement("input#password", password);
    console.log("Password filled successfully");
  } catch (error) {
    console.error("Failed to fill password:", error);
  }
}

// Example: Fill a search input field
async function fillSearchField(searchTerm: string) {
  try {
    await FillElement("input[placeholder='Search']", searchTerm);
    console.log("Search field filled successfully");
  } catch (error) {
    console.error("Failed to fill search field:", error);
  }
}
```

## Selector Examples

You can use various CSS selectors to target elements:

1. By ID: `#elementId`
2. By Class: `.className`
3. By Tag: `input`, `textarea`, etc.
4. By Attribute: `[placeholder='Search']`
5. By Combination: `div.form-group input[type='text']`

## Error Handling

The method will return an error if:
- The page is not initialized
- The element is not found
- The element is not visible
- The element cannot be filled (e.g., it's not an input element)

Always wrap your calls in try/catch blocks to handle these potential errors.
